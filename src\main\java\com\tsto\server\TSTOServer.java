package com.tsto.server;

import com.sun.net.httpserver.HttpServer;
import com.sun.net.httpserver.HttpHandler;
import com.sun.net.httpserver.HttpExchange;
import java.io.IOException;
import java.io.OutputStream;
import java.net.InetSocketAddress;
import java.util.concurrent.Executors;
import java.util.logging.Logger;
import java.util.logging.Level;
import java.security.SecureRandom;
import java.util.Base64;
import java.nio.charset.StandardCharsets;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import java.util.HashMap;
import org.json.JSONObject;

public class TSTOServer {
    private static final Logger LOGGER = Logger.getLogger(TSTOServer.class.getName());
    private HttpServer server;
    private int serverPort;
    private final SecureRandom secureRandom;
    private final GameManager gameManager;
    private final Gson gson;
    private final ServerConfig config;
    public TSTOServer(ServerConfig config) {
        this.secureRandom = new SecureRandom();
        this.gameManager = GameManager.getInstance();
        this.gson = new Gson();
        this.config = config;
        config.isDebugMode();
        config.getServerIp();
        this.serverPort = config.getPort();
        GameState.getInstance();
        new HashMap<>();
        FriendSystem.getInstance();
    }

    public boolean initialize(int port) {
        try {
            server = HttpServer.create(new InetSocketAddress(port), 0);
            
            // Create context for root endpoint
            server.createContext("/", new RootHandler());
            
            // Create context for API endpoints
            server.createContext("/api/login", new HttpHandler() {
                @Override
                public void handle(HttpExchange exchange) throws IOException {
                    handleLogin(exchange);
                }
            });

            server.createContext("/api/register", new HttpHandler() {
                @Override
                public void handle(HttpExchange exchange) throws IOException {
                    handleRegister(exchange);
                }
            });

            // Create context for game API endpoints
            GameAPI gameApi = new GameAPI(gameManager);
            server.createContext("/api/game", gameApi);
            
            // Set executor
            server.setExecutor(Executors.newFixedThreadPool(10));
            
            LOGGER.info("Server initialized on port " + port);
            return true;
        } catch (IOException e) {
            LOGGER.log(Level.SEVERE, "Failed to initialize server on port " + port, e);
            return false;
        }
    }

    public void start() {
        if (server != null) {
            server.start();
            LOGGER.info("TSTO server started on port " + serverPort);
        } else {
            LOGGER.severe("Server not initialized. Call initialize() first.");
        }
    }

    public void stop() {
        if (server != null) {
            server.stop(0);
            LOGGER.info("TSTO server stopped");
        }
    }

    private class RootHandler implements HttpHandler {
        @Override
        public void handle(HttpExchange exchange) throws IOException {
            String path = exchange.getRequestURI().getPath();
            if (path.equals("/") || path.equals("/index.html")) {
                serveFile(exchange, "static/index.html");
            } else if (path.endsWith(".js")) {
                serveFile(exchange, "static" + path);
            } else if (path.endsWith(".css")) {
                serveFile(exchange, "static" + path);
            } else {
                String response = "TSTO Private Server";
                exchange.sendResponseHeaders(200, response.length());
                exchange.getResponseBody().write(response.getBytes());
                exchange.getResponseBody().close();
            }
        }

        private void serveFile(HttpExchange exchange, String filePath) throws IOException {
            try {
                java.io.InputStream is = getClass().getClassLoader().getResourceAsStream(filePath);
                if (is == null) {
                    sendResponse(exchange, 404, "File not found");
                    return;
                }

                byte[] fileContent = is.readAllBytes();
                String contentType = "text/html";
                if (filePath.endsWith(".js")) {
                    contentType = "application/javascript";
                } else if (filePath.endsWith(".css")) {
                    contentType = "text/css";
                }

                exchange.getResponseHeaders().set("Content-Type", contentType);
                exchange.sendResponseHeaders(200, fileContent.length);
                try (OutputStream os = exchange.getResponseBody()) {
                    os.write(fileContent);
                }
            } catch (Exception e) {
                LOGGER.log(Level.SEVERE, "Error serving file: " + filePath, e);
                sendResponse(exchange, 500, "Internal server error");
            }
        }
    }

    private void sendResponse(HttpExchange exchange, int statusCode, String response) throws IOException {
        exchange.getResponseHeaders().set("Content-Type", "application/json");
        byte[] responseBytes = response.getBytes(StandardCharsets.UTF_8);
        exchange.sendResponseHeaders(statusCode, responseBytes.length);
        try (OutputStream os = exchange.getResponseBody()) {
            os.write(responseBytes);
        }
    }

    private String generateRandomId() {
        byte[] randomBytes = new byte[16];
        secureRandom.nextBytes(randomBytes);
        return Base64.getUrlEncoder().withoutPadding().encodeToString(randomBytes);
    }

    private class DirectionHandler implements HttpHandler {
        @Override
        public void handle(HttpExchange exchange) throws IOException {
            sendResponse(exchange, 200, "{\"status\":\"ok\"}");
        }
    }

    private class LobbyTimeHandler implements HttpHandler {
        @Override
        public void handle(HttpExchange exchange) throws IOException {
            sendResponse(exchange, 200, "{\"status\":\"ok\"}");
        }
    }

    private class FriendDataHandler implements HttpHandler {
        @Override
        public void handle(HttpExchange exchange) throws IOException {
            sendResponse(exchange, 200, "{\"status\":\"ok\"}");
        }
    }

    private class PluginEventHandler implements HttpHandler {
        @Override
        public void handle(HttpExchange exchange) throws IOException {
            sendResponse(exchange, 200, "{\"status\":\"ok\"}");
        }
    }

    // Friend System Methods
    private class FriendRequestHandler implements HttpHandler {
        @Override
        public void handle(HttpExchange exchange) throws IOException {
            try {
                String requestBody = new String(exchange.getRequestBody().readAllBytes());
                JSONObject request = new JSONObject(requestBody);
                
                String fromUserId = request.getString("fromUserId");
                String toUserId = request.getString("toUserId");
                
                FriendSystem.getInstance().sendFriendRequest(fromUserId, toUserId);
                
                sendResponse(exchange, 200, "Friend request sent successfully");
            } catch (Exception e) {
                LOGGER.log(Level.SEVERE, "Error handling friend request", e);
                sendResponse(exchange, 500, "Internal server error");
            }
        }
    }

    private class AcceptFriendHandler implements HttpHandler {
        @Override
        public void handle(HttpExchange exchange) throws IOException {
            try {
                String requestBody = new String(exchange.getRequestBody().readAllBytes());
                JSONObject request = new JSONObject(requestBody);
                
                String userId = request.getString("userId");
                String friendId = request.getString("friendId");
                
                FriendSystem.getInstance().acceptFriendRequest(userId, friendId);
                
                sendResponse(exchange, 200, "Friend request accepted");
            } catch (Exception e) {
                LOGGER.log(Level.SEVERE, "Error accepting friend request", e);
                sendResponse(exchange, 500, "Internal server error");
            }
        }
    }

    private class RejectFriendHandler implements HttpHandler {
        @Override
        public void handle(HttpExchange exchange) throws IOException {
            try {
                String requestBody = new String(exchange.getRequestBody().readAllBytes());
                JSONObject request = new JSONObject(requestBody);
                
                String userId = request.getString("userId");
                String friendId = request.getString("friendId");
                
                FriendSystem.getInstance().rejectFriendRequest(userId, friendId);
                
                sendResponse(exchange, 200, "Friend request rejected");
            } catch (Exception e) {
                LOGGER.log(Level.SEVERE, "Error rejecting friend request", e);
                sendResponse(exchange, 500, "Internal server error");
            }
        }
    }

    private class StartQuestHandler implements HttpHandler {
        @Override
        public void handle(HttpExchange exchange) throws IOException {
            try {
                String requestBody = new String(exchange.getRequestBody().readAllBytes());
                JSONObject request = new JSONObject(requestBody);
                
                String userId = request.getString("userId");
                String questId = request.getString("questId");
                
                if (gameManager.startQuest(userId, questId)) {
                    sendResponse(exchange, 200, "Quest started successfully");
                } else {
                    sendResponse(exchange, 400, "Failed to start quest");
                }
            } catch (Exception e) {
                LOGGER.log(Level.SEVERE, "Error starting quest", e);
                sendResponse(exchange, 500, "Internal server error");
            }
        }
    }

    private class CompleteQuestHandler implements HttpHandler {
        @Override
        public void handle(HttpExchange exchange) throws IOException {
            try {
                String requestBody = new String(exchange.getRequestBody().readAllBytes());
                JSONObject request = new JSONObject(requestBody);
                
                String userId = request.getString("userId");
                String questId = request.getString("questId");
                
                if (gameManager.completeQuest(userId, questId)) {
                    sendResponse(exchange, 200, "Quest completed successfully");
                } else {
                    sendResponse(exchange, 400, "Failed to complete quest");
                }
            } catch (Exception e) {
                LOGGER.log(Level.SEVERE, "Error completing quest", e);
                sendResponse(exchange, 500, "Internal server error");
            }
        }
    }

    private class PlaceBuildingHandler implements HttpHandler {
        @Override
        public void handle(HttpExchange exchange) throws IOException {
            try {
                String requestBody = new String(exchange.getRequestBody().readAllBytes());
                JSONObject request = new JSONObject(requestBody);
                
                String userId = request.getString("userId");
                String buildingId = request.getString("buildingId");
                int x = request.getInt("x");
                int y = request.getInt("y");
                
                Building building = gameManager.getBuilding(buildingId);
                if (building != null) {
                    building.setX(x);
                    building.setY(y);
                    building.setPlaced(true);
                    sendResponse(exchange, 200, "Building placed successfully");
                } else {
                    sendResponse(exchange, 400, "Failed to place building");
                }
            } catch (Exception e) {
                LOGGER.log(Level.SEVERE, "Error placing building", e);
                sendResponse(exchange, 500, "Internal server error");
            }
        }
    }

    private class AssignTaskHandler implements HttpHandler {
        @Override
        public void handle(HttpExchange exchange) throws IOException {
            try {
                String requestBody = new String(exchange.getRequestBody().readAllBytes());
                JSONObject request = new JSONObject(requestBody);
                
                String userId = request.getString("userId");
                String characterId = request.getString("characterId");
                String taskId = request.getString("taskId");
                
                if (gameManager.assignTask(userId, characterId, taskId)) {
                    sendResponse(exchange, 200, "Task assigned successfully");
                } else {
                    sendResponse(exchange, 400, "Failed to assign task");
                }
            } catch (Exception e) {
                LOGGER.log(Level.SEVERE, "Error assigning task", e);
                sendResponse(exchange, 500, "Internal server error");
            }
        }
    }

    private void sendJsonResponse(HttpExchange exchange, JSONObject response) throws IOException {
        byte[] responseBytes = response.toString().getBytes();
        exchange.getResponseHeaders().set("Content-Type", "application/json");
        exchange.sendResponseHeaders(200, responseBytes.length);
        try (OutputStream os = exchange.getResponseBody()) {
            os.write(responseBytes);
        }
    }

    private void sendErrorResponse(HttpExchange exchange, int code, String message) throws IOException {
        JSONObject response = new JSONObject();
        response.put("success", false);
        response.put("message", message);
        byte[] responseBytes = response.toString().getBytes();
        exchange.getResponseHeaders().set("Content-Type", "application/json");
        exchange.sendResponseHeaders(code, responseBytes.length);
        try (OutputStream os = exchange.getResponseBody()) {
            os.write(responseBytes);
        }
    }

    private void handleLogin(HttpExchange exchange) throws IOException {
        String requestBody = new String(exchange.getRequestBody().readAllBytes());
        JSONObject json = new JSONObject(requestBody);
        String username = json.getString("username");
        String password = json.getString("password");

        User user = gameManager.authenticateUser(username, password);
        JSONObject response = new JSONObject();

        if (user != null) {
            response.put("success", true);
            response.put("userId", user.getId());
            response.put("username", user.getUsername());
            response.put("level", user.getLevel());
            response.put("experience", user.getExperience());
            response.put("money", user.getMoney());
            response.put("donuts", user.getDonuts());
        } else {
            response.put("success", false);
            response.put("message", "Invalid username or password");
        }

        sendJsonResponse(exchange, response);
    }

    private void handleRegister(HttpExchange exchange) throws IOException {
        if (!exchange.getRequestMethod().equals("POST")) {
            sendResponse(exchange, 405, "{\"error\":\"Method not allowed\"}");
            return;
        }

        String requestBody = new String(exchange.getRequestBody().readAllBytes());
        JSONObject json = new JSONObject(requestBody);
        String username = json.getString("username");
        String password = json.getString("password");

        User user = gameManager.createUser(username, password);
        JSONObject response = new JSONObject();

        if (user != null) {
            response.put("success", true);
            response.put("userId", user.getId());
            response.put("username", user.getUsername());
            response.put("level", user.getLevel());
            response.put("experience", user.getExperience());
            response.put("money", user.getMoney());
            response.put("donuts", user.getDonuts());
        } else {
            response.put("success", false);
            response.put("message", "Failed to create user");
        }

        sendJsonResponse(exchange, response);
    }

    private void handlePurchase(HttpExchange exchange) throws IOException {
        String requestBody = new String(exchange.getRequestBody().readAllBytes());
        JSONObject json = new JSONObject(requestBody);
        String userId = json.getString("userId");
        String itemId = json.getString("itemId");

        boolean success = gameManager.purchaseItem(userId, itemId);
        JSONObject response = new JSONObject();
        response.put("success", success);
        
        if (success) {
            User user = gameManager.getUser(userId);
            response.put("message", "Item purchased successfully");
            response.put("money", user.getMoney());
            response.put("donuts", user.getDonuts());
        } else {
            response.put("message", "Failed to purchase item");
        }

        sendJsonResponse(exchange, response);
    }

    private void handleQuestStart(HttpExchange exchange) throws IOException {
        String requestBody = new String(exchange.getRequestBody().readAllBytes());
        JSONObject json = new JSONObject(requestBody);
        String userId = json.getString("userId");
        String questId = json.getString("questId");

        boolean success = gameManager.startQuest(userId, questId);
        JSONObject response = new JSONObject();
        response.put("success", success);
        
        if (success) {
            response.put("message", "Quest started successfully");
        } else {
            response.put("message", "Failed to start quest");
        }

        sendJsonResponse(exchange, response);
    }

    private void handleQuestComplete(HttpExchange exchange) throws IOException {
        String requestBody = new String(exchange.getRequestBody().readAllBytes());
        JSONObject json = new JSONObject(requestBody);
        String userId = json.getString("userId");
        String questId = json.getString("questId");

        boolean success = gameManager.completeQuest(userId, questId);
        JSONObject response = new JSONObject();
        response.put("success", success);
        
        if (success) {
            User user = gameManager.getUser(userId);
            response.put("message", "Quest completed successfully");
            response.put("experience", user.getExperience());
            response.put("level", user.getLevel());
            response.put("money", user.getMoney());
        } else {
            response.put("message", "Failed to complete quest");
        }

        sendJsonResponse(exchange, response);
    }

    private void handleCharacterTask(HttpExchange exchange) throws IOException {
        String requestBody = new String(exchange.getRequestBody().readAllBytes());
        JSONObject json = new JSONObject(requestBody);
        String userId = json.getString("userId");
        String characterId = json.getString("characterId");
        String taskId = json.getString("taskId");
        long duration = json.getLong("duration");

        boolean success = gameManager.assignTask(userId, characterId, taskId);
        JSONObject response = new JSONObject();
        response.put("success", success);
        
        if (success) {
            Character character = gameManager.getCharacter(characterId);
            response.put("message", "Task assigned successfully");
            response.put("taskEndTime", character.getTaskEndTime());
        } else {
            response.put("message", "Failed to assign task");
        }

        sendJsonResponse(exchange, response);
    }
} 