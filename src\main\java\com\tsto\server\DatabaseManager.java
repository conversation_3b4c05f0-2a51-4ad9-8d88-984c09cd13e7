package com.tsto.server;

import java.sql.*;
import java.util.logging.Logger;
import java.util.logging.Level;
import java.io.File;

public class DatabaseManager {
    private static final Logger logger = Logger.getLogger(DatabaseManager.class.getName());
    private static final String DB_DIR = "db";
    private static final String DB_FILE = "users.db";
    private static final String DB_URL = "jdbc:sqlite:" + DB_DIR + File.separator + DB_FILE;
    private static DatabaseManager instance;
    private Connection connection;

    private DatabaseManager() {
        initializeDatabase();
    }

    public static DatabaseManager getInstance() {
        if (instance == null) {
            instance = new DatabaseManager();
        }
        return instance;
    }

    private void initializeDatabase() {
        try {
            // Create db directory if it doesn't exist
            File dbDir = new File(DB_DIR);
            if (!dbDir.exists()) {
                dbDir.mkdirs();
            }

            // Create database connection
            connection = DriverManager.getConnection(DB_URL);
            
            // Create users table if it doesn't exist
            String createTableSQL = "CREATE TABLE IF NOT EXISTS users (" +
                "id TEXT PRIMARY KEY," +
                "username TEXT UNIQUE NOT NULL," +
                "password_hash TEXT NOT NULL," +
                "level INTEGER DEFAULT 1," +
                "experience INTEGER DEFAULT 0," +
                "money INTEGER DEFAULT 1000," +
                "donuts INTEGER DEFAULT 10" +
                ")";
            
            try (Statement statement = connection.createStatement()) {
                statement.execute(createTableSQL);
            }
            
            logger.info("Database initialized successfully");
        } catch (SQLException e) {
            logger.log(Level.SEVERE, "Error initializing database", e);
        }
    }

    public boolean createUser(String id, String username, String passwordHash, int level, int experience, int money, int donuts) {
        try {
            String sql = "INSERT INTO users (id, username, password_hash, level, experience, money, donuts) VALUES (?, ?, ?, ?, ?, ?, ?)";
            PreparedStatement stmt = connection.prepareStatement(sql);
            stmt.setString(1, id);
            stmt.setString(2, username);
            stmt.setString(3, passwordHash);
            stmt.setInt(4, level);
            stmt.setInt(5, experience);
            stmt.setInt(6, money);
            stmt.setInt(7, donuts);
            return stmt.executeUpdate() > 0;
        } catch (SQLException e) {
            logger.log(Level.SEVERE, "Error creating user", e);
            return false;
        }
    }

    public User authenticateUser(String username, String passwordHash) {
        try {
            String sql = "SELECT id, username, level, experience, money, donuts FROM users WHERE username = ? AND password_hash = ?";
            PreparedStatement stmt = connection.prepareStatement(sql);
            stmt.setString(1, username);
            stmt.setString(2, passwordHash);
            ResultSet rs = stmt.executeQuery();
            if (rs.next()) {
                return new User(
                    rs.getString("id"),
                    rs.getString("username"),
                    rs.getInt("level"),
                    rs.getInt("experience"),
                    rs.getInt("money"),
                    rs.getInt("donuts")
                );
            }
            return null;
        } catch (SQLException e) {
            logger.log(Level.SEVERE, "Error authenticating user", e);
            return null;
        }
    }

    public User getUser(String userId) {
        String sql = "SELECT * FROM users WHERE id = ?";
        try (PreparedStatement statement = connection.prepareStatement(sql)) {
            statement.setString(1, userId);
            try (ResultSet resultSet = statement.executeQuery()) {
                if (resultSet.next()) {
                    return new User(
                        resultSet.getString("id"),
                        resultSet.getString("username"),
                        resultSet.getInt("level"),
                        resultSet.getInt("experience"),
                        resultSet.getInt("money"),
                        resultSet.getInt("donuts")
                    );
                }
            }
        } catch (SQLException e) {
            logger.log(Level.SEVERE, "Error getting user", e);
        }
        return null;
    }

    public void close() {
        try {
            if (connection != null && !connection.isClosed()) {
                connection.close();
            }
        } catch (SQLException e) {
            logger.log(Level.SEVERE, "Error closing database connection", e);
        }
    }
} 