package com.tsto.server;

import java.io.Serializable;

public class InventoryItem implements Serializable {
    private String id;
    private String name;
    private String type;
    private int quantity;
    private boolean isEquipped;

    public InventoryItem(String id, String name, String type) {
        this.id = id;
        this.name = name;
        this.type = type;
        this.quantity = 1;
        this.isEquipped = false;
    }

    public String getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public String getType() {
        return type;
    }

    public int getQuantity() {
        return quantity;
    }

    public void setQuantity(int quantity) {
        if (quantity >= 0) {
            this.quantity = quantity;
        }
    }

    public boolean isEquipped() {
        return isEquipped;
    }

    public void setEquipped(boolean equipped) {
        this.isEquipped = equipped;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        InventoryItem that = (InventoryItem) o;
        return id.equals(that.id);
    }

    @Override
    public int hashCode() {
        return id.hashCode();
    }
} 