package com.tsto.server;

import java.util.HashMap;
import java.util.Map;

public class Character {
    private final String id;
    private final String name;
    private String ownerId;
    private int level;
    private int x;
    private int y;
    private boolean isAvailable;
    private String currentTask;
    private long taskEndTime;
    private Map<String, Integer> tasks; // taskId -> duration in seconds
    private double taskProgress;
    private boolean isBusy;
    private final Map<String, Object> properties;

    public Character(String id, String name) {
        this.id = id;
        this.name = name;
        this.level = 1;
        this.x = 0;
        this.y = 0;
        this.isAvailable = true;
        this.currentTask = null;
        this.taskEndTime = 0;
        this.tasks = new HashMap<>();
        this.taskProgress = 0.0;
        this.isBusy = false;
        this.properties = new HashMap<>();
    }

    // Getters and Setters
    public String getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public String getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(String ownerId) {
        this.ownerId = ownerId;
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public int getX() {
        return x;
    }

    public void setX(int x) {
        this.x = x;
    }

    public int getY() {
        return y;
    }

    public void setY(int y) {
        this.y = y;
    }

    public boolean isAvailable() {
        return isAvailable && (System.currentTimeMillis() > taskEndTime);
    }

    public void setAvailable(boolean available) {
        this.isAvailable = available;
    }

    public String getCurrentTask() {
        return currentTask;
    }

    public void setCurrentTask(String taskId, long duration) {
        this.currentTask = taskId;
        this.taskEndTime = System.currentTimeMillis() + duration;
        this.isBusy = true;
    }

    public void completeTask() {
        this.currentTask = null;
        this.taskEndTime = 0;
        this.isAvailable = true;
    }

    public long getTaskEndTime() {
        return taskEndTime;
    }

    public void setTaskEndTime(long taskEndTime) {
        this.taskEndTime = taskEndTime;
    }

    public long getRemainingTaskTime() {
        long remaining = taskEndTime - System.currentTimeMillis();
        return Math.max(0, remaining);
    }

    public Map<String, Integer> getTasks() {
        return new HashMap<>(tasks);
    }

    public void addTask(String taskId, int duration) {
        tasks.put(taskId, duration);
    }

    public void removeTask(String taskId) {
        tasks.remove(taskId);
    }

    public int getTaskDuration(String taskId) {
        return tasks.getOrDefault(taskId, 0);
    }

    public double getTaskProgress() {
        return taskProgress;
    }

    public void updateTaskProgress(double progress) {
        this.taskProgress = Math.min(1.0, Math.max(0.0, progress));
    }

    public boolean isTaskComplete() {
        return taskProgress >= 1.0;
    }

    public void startTask(String taskId) {
        if (tasks.containsKey(taskId)) {
            this.currentTask = taskId;
            this.taskProgress = 0.0;
            this.taskEndTime = System.currentTimeMillis() + (tasks.get(taskId) * 1000L);
        }
    }

    public boolean isBusy() {
        return isBusy || (currentTask != null && System.currentTimeMillis() < taskEndTime);
    }

    public void setBusy(boolean busy) {
        this.isBusy = busy;
    }

    public void setProperty(String key, Object value) {
        properties.put(key, value);
    }

    public Object getProperty(String key) {
        return properties.get(key);
    }
} 