import sqlite3
import json
from datetime import datetime
import os

class Database:
    def __init__(self, db_path='tsto_game.db'):
        self.db_path = db_path
        self.init_db()

    def init_db(self):
        """Initialize the database with required tables"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Users table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                email TEXT UNIQUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_login TIMESTAMP
            )
        ''')

        # Game state table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS game_states (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                money INTEGER DEFAULT 1000,
                donuts INTEGER DEFAULT 10,
                buildings TEXT DEFAULT '[]',
                characters TEXT DEFAULT '[]',
                inventory TEXT DEFAULT '[]',
                last_save TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')

        # Buildings table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS buildings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                building_type TEXT NOT NULL,
                level INTEGER DEFAULT 1,
                x INTEGER NOT NULL,
                y INTEGER NOT NULL,
                last_collection TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')

        # Characters table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS characters (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                character_type TEXT NOT NULL,
                level INTEGER DEFAULT 1,
                x INTEGER,
                y INTEGER,
                task_end_time TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')

        # Inventory table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS inventory (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                item_type TEXT NOT NULL,
                item_id TEXT NOT NULL,
                quantity INTEGER DEFAULT 1,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')

        conn.commit()
        conn.close()

    def create_user(self, username, password_hash, email=None):
        """Create a new user"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        try:
            cursor.execute('''
                INSERT INTO users (username, password_hash, email)
                VALUES (?, ?, ?)
            ''', (username, password_hash, email))
            user_id = cursor.lastrowid
            
            # Create initial game state
            cursor.execute('''
                INSERT INTO game_states (user_id)
                VALUES (?)
            ''', (user_id,))
            
            conn.commit()
            return user_id
        except sqlite3.IntegrityError:
            return None
        finally:
            conn.close()

    def get_user(self, username):
        """Get user by username"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM users WHERE username = ?', (username,))
        user = cursor.fetchone()
        conn.close()
        return user

    def get_game_state(self, user_id):
        """Get user's game state"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM game_states WHERE user_id = ?', (user_id,))
        state = cursor.fetchone()
        conn.close()
        return state

    def update_game_state(self, user_id, money=None, donuts=None, buildings=None, characters=None, inventory=None):
        """Update user's game state"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        updates = []
        params = []
        
        if money is not None:
            updates.append('money = ?')
            params.append(money)
        if donuts is not None:
            updates.append('donuts = ?')
            params.append(donuts)
        if buildings is not None:
            updates.append('buildings = ?')
            params.append(json.dumps(buildings))
        if characters is not None:
            updates.append('characters = ?')
            params.append(json.dumps(characters))
        if inventory is not None:
            updates.append('inventory = ?')
            params.append(json.dumps(inventory))
            
        updates.append('last_save = ?')
        params.append(datetime.now().isoformat())
        params.append(user_id)
        
        query = f'''
            UPDATE game_states 
            SET {', '.join(updates)}
            WHERE user_id = ?
        '''
        
        cursor.execute(query, params)
        conn.commit()
        conn.close()

    def add_building(self, user_id, building_type, x, y):
        """Add a new building to user's town"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute('''
            INSERT INTO buildings (user_id, building_type, x, y)
            VALUES (?, ?, ?, ?)
        ''', (user_id, building_type, x, y))
        building_id = cursor.lastrowid
        conn.commit()
        conn.close()
        return building_id

    def get_buildings(self, user_id):
        """Get all buildings for a user"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM buildings WHERE user_id = ?', (user_id,))
        buildings = cursor.fetchall()
        conn.close()
        return buildings

    def update_building(self, building_id, level=None, x=None, y=None):
        """Update building properties"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        updates = []
        params = []
        
        if level is not None:
            updates.append('level = ?')
            params.append(level)
        if x is not None:
            updates.append('x = ?')
            params.append(x)
        if y is not None:
            updates.append('y = ?')
            params.append(y)
            
        params.append(building_id)
        
        query = f'''
            UPDATE buildings 
            SET {', '.join(updates)}
            WHERE id = ?
        '''
        
        cursor.execute(query, params)
        conn.commit()
        conn.close()

    def add_character(self, user_id, character_type, x=None, y=None):
        """Add a new character to user's town"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute('''
            INSERT INTO characters (user_id, character_type, x, y)
            VALUES (?, ?, ?, ?)
        ''', (user_id, character_type, x, y))
        character_id = cursor.lastrowid
        conn.commit()
        conn.close()
        return character_id

    def get_characters(self, user_id):
        """Get all characters for a user"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM characters WHERE user_id = ?', (user_id,))
        characters = cursor.fetchall()
        conn.close()
        return characters

    def update_character(self, character_id, level=None, x=None, y=None, task_end_time=None):
        """Update character properties"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        updates = []
        params = []
        
        if level is not None:
            updates.append('level = ?')
            params.append(level)
        if x is not None:
            updates.append('x = ?')
            params.append(x)
        if y is not None:
            updates.append('y = ?')
            params.append(y)
        if task_end_time is not None:
            updates.append('task_end_time = ?')
            params.append(task_end_time)
            
        params.append(character_id)
        
        query = f'''
            UPDATE characters 
            SET {', '.join(updates)}
            WHERE id = ?
        '''
        
        cursor.execute(query, params)
        conn.commit()
        conn.close()

    def add_inventory_item(self, user_id, item_type, item_id, quantity=1):
        """Add or update inventory item"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Check if item exists
        cursor.execute('''
            SELECT id, quantity FROM inventory 
            WHERE user_id = ? AND item_type = ? AND item_id = ?
        ''', (user_id, item_type, item_id))
        
        existing = cursor.fetchone()
        
        if existing:
            # Update quantity
            new_quantity = existing[1] + quantity
            cursor.execute('''
                UPDATE inventory 
                SET quantity = ? 
                WHERE id = ?
            ''', (new_quantity, existing[0]))
        else:
            # Add new item
            cursor.execute('''
                INSERT INTO inventory (user_id, item_type, item_id, quantity)
                VALUES (?, ?, ?, ?)
            ''', (user_id, item_type, item_id, quantity))
            
        conn.commit()
        conn.close()

    def get_inventory(self, user_id):
        """Get user's inventory"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM inventory WHERE user_id = ?', (user_id,))
        inventory = cursor.fetchall()
        conn.close()
        return inventory

# Create database instance
db = Database() 