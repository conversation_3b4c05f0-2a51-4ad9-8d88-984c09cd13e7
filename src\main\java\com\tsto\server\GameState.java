package com.tsto.server;

import java.util.ArrayList;
import java.util.List;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Logger;
import java.util.logging.Level;
import org.json.JSONArray;
import org.json.JSONObject;

public class GameState {
    private static final Logger LOGGER = Logger.getLogger(GameState.class.getName());
    private static GameState instance;
    private final Map<String, Building> buildings;
    private final Map<String, Character> characters;
    private final Map<String, Quest> quests;
    private final Map<String, Event> events;
    private final Map<String, ShopItem> shopItems;
    private final Map<String, Achievement> achievements;
    private final DatabaseManager dbManager;
    private int money;
    private int donuts;
    private int level;
    private int experience;
    private List<Building> buildingsList;
    private List<Quest> questsList;
    private List<Character> charactersList;

    private GameState() {
        this.buildings = new ConcurrentHashMap<>();
        this.characters = new ConcurrentHashMap<>();
        this.quests = new ConcurrentHashMap<>();
        this.events = new ConcurrentHashMap<>();
        this.shopItems = new ConcurrentHashMap<>();
        this.achievements = new ConcurrentHashMap<>();
        this.dbManager = DatabaseManager.getInstance();
        this.money = 1000;
        this.donuts = 10;
        this.level = 1;
        this.experience = 0;
        this.buildingsList = new ArrayList<>();
        this.questsList = new ArrayList<>();
        this.charactersList = new ArrayList<>();
        initializeDefaultData();
    }

    public static synchronized GameState getInstance() {
        if (instance == null) {
            instance = new GameState();
        }
        return instance;
    }

    private void initializeDefaultData() {
        // Initialize default shop items
        shopItems.put("basic_house", new ShopItem("basic_house", "Basic House", "building", 1000, "A simple starter house", "/images/basic_house.png", false, 1));
        shopItems.put("krusty_burger", new ShopItem("krusty_burger", "Krusty Burger", "building", 5000, "A fast food restaurant", "/images/krusty_burger.png", false, 2));
        shopItems.put("donut_shop", new ShopItem("donut_shop", "Lard Lad Donuts", "building", 7500, "A donut shop", "/images/donut_shop.png", false, 3));
        shopItems.put("premium_house", new ShopItem("premium_house", "Premium House", "building", 50, "A luxurious house", "/images/premium_house.png", true, 1));
        
        LOGGER.info("Initialized " + shopItems.size() + " shop items");

        // Initialize default buildings
        Building basicHouse = new Building("basic_house", "Basic House");
        buildings.put(basicHouse.getId(), basicHouse);
        buildingsList.add(basicHouse);

        // Initialize default quests
        Quest firstQuest = new Quest("first_quest", "Welcome to Springfield", "tutorial");
        firstQuest.addObjective("Place your first building", 1);
        firstQuest.addObjective("Complete a character task", 1);
        firstQuest.addReward("money", 500);
        firstQuest.addReward("experience", 100);
        quests.put(firstQuest.getId(), firstQuest);

        // Initialize default achievements
        achievements.put("first_login", new Achievement("first_login", "First Login", "Log in for the first time", 100, 50, 1));
        achievements.put("first_building", new Achievement("first_building", "First Building", "Build your first building", 200, 100, 2));
    }

    private void initializeShopItems() {
        shopItems.put("donut_shop", new ShopItem("donut_shop", "Donut Shop", "building", 100, "A shop that sells donuts", "/images/donut_shop.png", false, 1));
        shopItems.put("kwik_e_mart", new ShopItem("kwik_e_mart", "Kwik-E-Mart", "building", 150, "Convenience store run by Apu", "/images/kwik_e_mart.png", false, 2));
        shopItems.put("nuclear_plant", new ShopItem("nuclear_plant", "Nuclear Power Plant", "building", 500, "Springfield's nuclear power plant", "/images/nuclear_plant.png", false, 5));
        shopItems.put("premium_statue", new ShopItem("premium_statue", "Jebediah Statue", "decoration", 100, "Statue of town founder", "/images/statue.png", true, 1));
    }

    public User getUser(String userId) {
        return dbManager.getUser(userId);
    }

    public void addUser(User user) {
        // Users are now managed by the database
        LOGGER.info("User added to database: " + user.getUsername());
    }

    public void removeUser(String userId) {
        // Users are now managed by the database
        LOGGER.info("User removed from database: " + userId);
    }

    public Map<String, Building> getBuildings() {
        return new HashMap<>(buildings);
    }

    public Building getBuilding(String buildingId) {
        return buildings.get(buildingId);
    }

    public void addBuilding(Building building) {
        buildings.put(building.getId(), building);
        buildingsList.add(building);
    }

    public void removeBuilding(String buildingId) {
        Building building = buildings.get(buildingId);
        if (building != null) {
            buildings.remove(buildingId);
            buildingsList.remove(building);
        }
    }

    public Map<String, Character> getCharacters() {
        return new HashMap<>(characters);
    }

    public Character getCharacter(String characterId) {
        return characters.get(characterId);
    }

    public void addCharacter(Character character) {
        characters.put(character.getId(), character);
        charactersList.add(character);
    }

    public void removeCharacter(String characterId) {
        Character character = characters.get(characterId);
        if (character != null) {
            characters.remove(characterId);
            charactersList.remove(character);
        }
    }

    public Map<String, Quest> getQuests() {
        return new HashMap<>(quests);
    }

    public Quest getQuest(String questId) {
        return quests.get(questId);
    }

    public void addQuest(Quest quest) {
        quests.put(quest.getId(), quest);
        questsList.add(quest);
    }

    public void removeQuest(String questId) {
        Quest quest = quests.get(questId);
        if (quest != null) {
            quests.remove(questId);
            questsList.remove(quest);
        }
    }

    public Map<String, Event> getEvents() {
        return new HashMap<>(events);
    }

    public Event getEvent(String eventId) {
        return events.get(eventId);
    }

    public void addEvent(Event event) {
        events.put(event.getId(), event);
    }

    public void removeEvent(String eventId) {
        events.remove(eventId);
    }

    public Map<String, ShopItem> getShopItems() {
        return new HashMap<>(shopItems);
    }

    public Map<String, ShopItem> getAllShopItems() {
        return new HashMap<>(shopItems);
    }

    public ShopItem getShopItem(String itemId) {
        return shopItems.get(itemId);
    }

    public void addShopItem(ShopItem item) {
        shopItems.put(item.getId(), item);
    }

    public void removeShopItem(String itemId) {
        shopItems.remove(itemId);
    }

    public Map<String, Achievement> getAchievements() {
        return new HashMap<>(achievements);
    }

    public Achievement getAchievement(String achievementId) {
        return achievements.get(achievementId);
    }

    public void addAchievement(Achievement achievement) {
        achievements.put(achievement.getId(), achievement);
    }

    public void removeAchievement(String achievementId) {
        achievements.remove(achievementId);
    }

    public int getMoney() {
        return money;
    }

    public void addMoney(int amount) {
        this.money += amount;
    }

    public boolean spendMoney(int amount) {
        if (this.money >= amount) {
            this.money -= amount;
            return true;
        }
        return false;
    }

    public int getDonuts() {
        return donuts;
    }

    public void addDonuts(int amount) {
        this.donuts += amount;
    }

    public boolean spendDonuts(int amount) {
        if (this.donuts >= amount) {
            this.donuts -= amount;
            return true;
        }
        return false;
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public int getExperience() {
        return experience;
    }

    public void addExperience(int amount) {
        this.experience += amount;
        int newLevel = calculateLevel(experience);
        if (newLevel > level) {
            level = newLevel;
            LOGGER.info("Player leveled up to " + newLevel);
        }
    }

    private int calculateLevel(int exp) {
        return (int) Math.sqrt(exp / 100) + 1;
    }

    public boolean purchaseItem(String userId, String itemId) {
        User user = getUser(userId);
        ShopItem item = shopItems.get(itemId);
        
        if (user == null || item == null) {
            LOGGER.warning("Purchase failed: Invalid user or item");
            return false;
        }

        if (!item.canPurchase(user.getLevel(), user.getMoney(), user.getDonuts())) {
            LOGGER.warning("Purchase failed: Requirements not met");
            return false;
        }

        if (item.isPremium()) {
            if (user.spendDonuts(item.getPrice())) {
                user.addToInventory(item.getId(), item.getName(), item.getType());
                LOGGER.info("Item purchased successfully: " + itemId + " by " + user.getUsername());
                return true;
            }
        } else {
            if (user.spendMoney(item.getPrice())) {
                user.addToInventory(item.getId(), item.getName(), item.getType());
                LOGGER.info("Item purchased successfully: " + itemId + " by " + user.getUsername());
                return true;
            }
        }

        LOGGER.warning("Purchase failed: Insufficient funds");
        return false;
    }

    public List<Building> getBuildingsList() {
        return buildingsList;
    }

    public List<Quest> getQuestsList() {
        return questsList;
    }

    public List<Character> getCharactersList() {
        return charactersList;
    }

    public JSONObject getTownData(String userId) {
        JSONObject townData = new JSONObject();
        try {
            User user = getUser(userId);
            if (user != null) {
                // Add user's buildings
                JSONArray buildingsArray = new JSONArray();
                for (Building building : buildingsList) {
                    if (building.getOwnerId().equals(userId)) {
                        JSONObject buildingObj = new JSONObject();
                        buildingObj.put("id", building.getId());
                        buildingObj.put("name", building.getName());
                        buildingObj.put("x", building.getX());
                        buildingObj.put("y", building.getY());
                        buildingObj.put("level", building.getLevel());
                        buildingObj.put("isPlaced", building.isPlaced());
                        buildingObj.put("isUnderConstruction", building.isUnderConstruction());
                        buildingsArray.put(buildingObj);
                    }
                }
                townData.put("buildings", buildingsArray);

                // Add user's characters
                JSONArray charactersArray = new JSONArray();
                for (Character character : charactersList) {
                    if (character.getOwnerId().equals(userId)) {
                        JSONObject characterObj = new JSONObject();
                        characterObj.put("id", character.getId());
                        characterObj.put("name", character.getName());
                        characterObj.put("x", character.getX());
                        characterObj.put("y", character.getY());
                        characterObj.put("currentTask", character.getCurrentTask());
                        characterObj.put("taskProgress", character.getTaskProgress());
                        charactersArray.put(characterObj);
                    }
                }
                townData.put("characters", charactersArray);

                // Add user's quests
                JSONArray questsArray = new JSONArray();
                for (Quest quest : questsList) {
                    if (quest.getOwnerId().equals(userId)) {
                        JSONObject questObj = new JSONObject();
                        questObj.put("id", quest.getId());
                        questObj.put("name", quest.getName());
                        questObj.put("type", quest.getType());
                        questObj.put("isCompleted", quest.isCompleted());
                        questObj.put("progress", quest.getProgress());
                        questsArray.put(questObj);
                    }
                }
                townData.put("quests", questsArray);

                // Add user's stats
                JSONObject stats = new JSONObject();
                stats.put("level", user.getLevel());
                stats.put("experience", user.getExperience());
                stats.put("money", user.getMoney());
                stats.put("donuts", user.getDonuts());
                townData.put("stats", stats);
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Error getting town data for user " + userId, e);
        }
        return townData;
    }
}