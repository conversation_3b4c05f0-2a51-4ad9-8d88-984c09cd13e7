package com.tsto.server;

import java.io.Serializable;
import java.time.Instant;

public class Achievement implements Serializable {
    private String id;
    private String name;
    private String description;
    private int experienceReward;
    private int moneyReward;
    private int donutReward;
    private boolean isCompleted;
    private Instant completedAt;

    public Achievement(String id, String name, String description, int experienceReward, int moneyReward, int donutReward) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.experienceReward = experienceReward;
        this.moneyReward = moneyReward;
        this.donutReward = donutReward;
        this.isCompleted = false;
        this.completedAt = null;
    }

    public String getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public String getDescription() {
        return description;
    }

    public int getExperienceReward() {
        return experienceReward;
    }

    public int getMoneyReward() {
        return moneyReward;
    }

    public int getDonutReward() {
        return donutReward;
    }

    public boolean isCompleted() {
        return isCompleted;
    }

    public Instant getCompletedAt() {
        return completedAt;
    }

    public void complete() {
        if (!isCompleted) {
            this.isCompleted = true;
            this.completedAt = Instant.now();
        }
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Achievement that = (Achievement) o;
        return id.equals(that.id);
    }

    @Override
    public int hashCode() {
        return id.hashCode();
    }
} 