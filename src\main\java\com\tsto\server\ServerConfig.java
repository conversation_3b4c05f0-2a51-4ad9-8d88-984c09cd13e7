package com.tsto.server;

import java.io.FileReader;
import java.io.FileWriter;
import java.util.Properties;
import java.util.logging.Logger;
import java.util.logging.Level;

public class ServerConfig {
    private static final Logger LOGGER = Logger.getLogger(ServerConfig.class.getName());
    private static final String CONFIG_FILE = "server.properties";
    
    private int port;
    private String serverIp;
    private boolean debugMode;
    private int maxConnections;
    private String dataDirectory;
    
    public ServerConfig() {
        // Default values
        this.port = 8080;
        this.serverIp = "localhost";
        this.debugMode = false;
        this.maxConnections = 100;
        this.dataDirectory = "data";
    }
    
    public void load() {
        Properties props = new Properties();
        try {
            props.load(new FileReader(CONFIG_FILE));
            this.port = Integer.parseInt(props.getProperty("port", "8080"));
            this.serverIp = props.getProperty("serverIp", "localhost");
            this.debugMode = Boolean.parseBoolean(props.getProperty("debugMode", "false"));
            this.maxConnections = Integer.parseInt(props.getProperty("maxConnections", "100"));
            this.dataDirectory = props.getProperty("dataDirectory", "data");
            LOGGER.info("Configuration loaded successfully");
        } catch (Exception e) {
            LOGGER.warning("Failed to load configuration, using defaults");
            save(); // Save default configuration
        }
    }
    
    public void save() {
        Properties props = new Properties();
        props.setProperty("port", String.valueOf(port));
        props.setProperty("serverIp", serverIp);
        props.setProperty("debugMode", String.valueOf(debugMode));
        props.setProperty("maxConnections", String.valueOf(maxConnections));
        props.setProperty("dataDirectory", dataDirectory);
        
        try {
            props.store(new FileWriter(CONFIG_FILE), "TSTO Server Configuration");
            LOGGER.info("Configuration saved successfully");
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Failed to save configuration", e);
        }
    }
    
    // Getters and Setters
    public int getPort() { return port; }
    public void setPort(int port) { this.port = port; }
    
    public String getServerIp() { return serverIp; }
    public void setServerIp(String serverIp) { this.serverIp = serverIp; }
    
    public boolean isDebugMode() { return debugMode; }
    public void setDebugMode(boolean debugMode) { this.debugMode = debugMode; }
    
    public int getMaxConnections() { return maxConnections; }
    public void setMaxConnections(int maxConnections) { this.maxConnections = maxConnections; }
    
    public String getDataDirectory() { return dataDirectory; }
    public void setDataDirectory(String dataDirectory) { this.dataDirectory = dataDirectory; }
} 