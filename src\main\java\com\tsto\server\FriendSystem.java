package com.tsto.server;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.logging.Logger;
import java.util.logging.Level;

public class FriendSystem {
    private static final Logger LOGGER = Logger.getLogger(FriendSystem.class.getName());
    private static FriendSystem instance;
    private final Map<String, Set<String>> friendLists; // userId -> Set of friend userIds
    private final Map<String, Set<String>> pendingRequests; // userId -> Set of pending request userIds
    private final Map<String, Set<String>> visitedTowns; // userId -> Set of visited town userIds

    private FriendSystem() {
        this.friendLists = new HashMap<>();
        this.pendingRequests = new HashMap<>();
        this.visitedTowns = new HashMap<>();
    }

    public static synchronized FriendSystem getInstance() {
        if (instance == null) {
            instance = new FriendSystem();
        }
        return instance;
    }

    public boolean sendFriendRequest(String senderId, String receiverId) {
        if (senderId.equals(receiverId)) {
            LOGGER.log(Level.WARNING, "User {0} tried to send friend request to themselves", senderId);
            return false;
        }

        // Initialize sets if they don't exist
        pendingRequests.putIfAbsent(receiverId, new HashSet<>());
        friendLists.putIfAbsent(senderId, new HashSet<>());
        friendLists.putIfAbsent(receiverId, new HashSet<>());

        // Check if they're already friends or if request already exists
        if (friendLists.get(senderId).contains(receiverId) || 
            pendingRequests.get(receiverId).contains(senderId)) {
            return false;
        }

        pendingRequests.get(receiverId).add(senderId);
        LOGGER.log(Level.INFO, "Friend request sent from {0} to {1}", new Object[]{senderId, receiverId});
        return true;
    }

    public boolean acceptFriendRequest(String userId, String requesterId) {
        if (!pendingRequests.containsKey(userId) || 
            !pendingRequests.get(userId).contains(requesterId)) {
            return false;
        }

        // Add to both friend lists
        friendLists.get(userId).add(requesterId);
        friendLists.get(requesterId).add(userId);
        
        // Remove from pending requests
        pendingRequests.get(userId).remove(requesterId);
        
        LOGGER.log(Level.INFO, "Friend request accepted: {0} and {1} are now friends", 
            new Object[]{userId, requesterId});
        return true;
    }

    public boolean rejectFriendRequest(String userId, String requesterId) {
        if (!pendingRequests.containsKey(userId) || 
            !pendingRequests.get(userId).contains(requesterId)) {
            return false;
        }

        pendingRequests.get(userId).remove(requesterId);
        LOGGER.log(Level.INFO, "Friend request rejected: {0} rejected {1}", 
            new Object[]{userId, requesterId});
        return true;
    }

    public boolean removeFriend(String userId, String friendId) {
        if (!friendLists.containsKey(userId) || !friendLists.containsKey(friendId)) {
            return false;
        }

        friendLists.get(userId).remove(friendId);
        friendLists.get(friendId).remove(userId);
        
        LOGGER.log(Level.INFO, "Friendship removed: {0} and {1} are no longer friends", 
            new Object[]{userId, friendId});
        return true;
    }

    public Set<String> getFriends(String userId) {
        return friendLists.getOrDefault(userId, new HashSet<>());
    }

    public Set<String> getPendingRequests(String userId) {
        return pendingRequests.getOrDefault(userId, new HashSet<>());
    }

    public boolean visitTown(String visitorId, String townOwnerId) {
        if (!friendLists.containsKey(visitorId) || 
            !friendLists.get(visitorId).contains(townOwnerId)) {
            return false;
        }

        visitedTowns.putIfAbsent(visitorId, new HashSet<>());
        visitedTowns.get(visitorId).add(townOwnerId);
        
        LOGGER.log(Level.INFO, "Town visit: {0} visited {1}'s town", 
            new Object[]{visitorId, townOwnerId});
        return true;
    }

    public Set<String> getVisitedTowns(String userId) {
        return visitedTowns.getOrDefault(userId, new HashSet<>());
    }

    public boolean canVisitTown(String visitorId, String townOwnerId) {
        return friendLists.containsKey(visitorId) && 
               friendLists.get(visitorId).contains(townOwnerId);
    }
} 