package com.tsto.server;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

public class Quest {
    public static class QuestObjective {
        private String description;
        private int targetCount;
        private int currentCount;

        public QuestObjective(String description, int targetCount) {
            this.description = description;
            this.targetCount = targetCount;
            this.currentCount = 0;
        }

        public void incrementProgress() {
            if (currentCount < targetCount) {
                currentCount++;
            }
        }

        public void setProgress(int progress) {
            this.currentCount = Math.min(progress, targetCount);
        }

        public boolean isCompleted() {
            return currentCount >= targetCount;
        }

        public String getDescription() {
            return description;
        }

        public int getTargetCount() {
            return targetCount;
        }

        public int getCurrentCount() {
            return currentCount;
        }
    }

    private String id;
    private String name;
    private String type;
    private String description;
    private String ownerId;
    private List<QuestObjective> objectives;
    private Map<String, Integer> progress;
    private Map<String, Integer> rewards;
    private boolean isCompleted;
    private boolean isActive;

    public Quest(String id, String name, String type) {
        this.id = id;
        this.name = name;
        this.type = type;
        this.description = "";
        this.objectives = new ArrayList<>();
        this.progress = new HashMap<>();
        this.rewards = new HashMap<>();
        this.isCompleted = false;
        this.isActive = false;
    }

    public String getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public String getType() {
        return type;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(String ownerId) {
        this.ownerId = ownerId;
    }

    public void addObjective(String description, int targetCount) {
        objectives.add(new QuestObjective(description, targetCount));
        progress.put(description, 0);
    }

    public List<QuestObjective> getObjectives() {
        return new ArrayList<>(objectives);
    }

    public void updateProgress(String objective, int amount) {
        if (progress.containsKey(objective)) {
            int current = progress.get(objective);
            int required = objectives.stream()
                .filter(o -> o.getDescription().equals(objective))
                .findFirst()
                .map(QuestObjective::getTargetCount)
                .orElse(0);
            progress.put(objective, Math.min(current + amount, required));
            checkCompletion();
        }
    }

    public double getProgress() {
        if (objectives.isEmpty()) {
            return 0.0;
        }

        int totalRequired = objectives.stream()
            .mapToInt(QuestObjective::getTargetCount)
            .sum();
        int totalProgress = progress.values().stream()
            .mapToInt(Integer::intValue)
            .sum();

        return (double) totalProgress / totalRequired;
    }

    public void addReward(String type, int amount) {
        rewards.put(type, amount);
    }

    public Map<String, Integer> getRewards() {
        return new HashMap<>(rewards);
    }

    public boolean isCompleted() {
        return isCompleted;
    }

    public void setCompleted(boolean completed) {
        this.isCompleted = completed;
    }

    public void start() {
        isActive = true;
    }

    public void complete() {
        isCompleted = true;
        isActive = false;
    }

    public boolean isActive() {
        return isActive;
    }

    public void setActive(boolean active) {
        this.isActive = active;
    }

    public boolean checkCompletion() {
        if (isCompleted) {
            return true;
        }

        boolean allComplete = objectives.stream()
            .allMatch(objective -> progress.getOrDefault(objective.getDescription(), 0) >= objective.getTargetCount());

        if (allComplete) {
            isCompleted = true;
        }

        return isCompleted;
    }
} 