<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TSTO - Build Your Town</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <div class="game-header">
            <div class="player-info">
                <span id="player-name"></span>
                <span id="player-money">$1000</span>
                <span id="player-donuts">10</span>
            </div>
            <div class="game-controls">
                <button class="save-btn">Save Game</button>
                <button class="load-btn">Load Game</button>
                <button class="logout-btn">Logout</button>
                <button class="back-btn">Back to Main</button>
            </div>
        </div>
        
        <div class="town-container">
            <div class="town-sidebar">
                <div class="building-categories">
                    <h3>Building Categories</h3>
                    <div class="category-list">
                        <div class="category-item active" data-category="residential">Residential</div>
                        <div class="category-item" data-category="commercial">Commercial</div>
                        <div class="category-item" data-category="industrial">Industrial</div>
                        <div class="category-item" data-category="entertainment">Entertainment</div>
                        <div class="category-item" data-category="education">Education</div>
                        <div class="category-item" data-category="nature">Nature</div>
                    </div>
                </div>
                
                <div class="building-list">
                    <h3>Available Buildings</h3>
                    <div id="buildingsList" class="buildings-list">
                        <!-- Buildings will be populated by JavaScript -->
                    </div>
                </div>
            </div>

            <div class="town-main">
                <div class="town-canvas" id="townCanvas">
                    <div class="town-grid">
                        <!-- Grid cells will be populated by JavaScript -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Game state
        let gameState = {
            money: 1000,
            donuts: 10,
            buildings: [],
            lastSave: Date.now()
        };

        // Building definitions
        const BUILDINGS = {
            residential: {
                house: { cost: 500, income: 10, interval: 60, name: 'House', size: 1, baseIncome: 10 },
                apartment: { cost: 1500, income: 30, interval: 45, name: 'Apartment', size: 2, baseIncome: 30 },
                mansion: { cost: 5000, income: 100, interval: 30, name: 'Mansion', size: 3, baseIncome: 100 }
            },
            commercial: {
                store: { cost: 1000, income: 25, interval: 30, name: 'Store', size: 1, baseIncome: 25 },
                mall: { cost: 3000, income: 75, interval: 20, name: 'Mall', size: 2, baseIncome: 75 },
                supermarket: { cost: 8000, income: 200, interval: 15, name: 'Supermarket', size: 3, baseIncome: 200 }
            },
            industrial: {
                factory: { cost: 2000, income: 50, interval: 15, name: 'Factory', size: 1, baseIncome: 50 },
                power_plant: { cost: 5000, income: 150, interval: 10, name: 'Power Plant', size: 2, baseIncome: 150 },
                refinery: { cost: 10000, income: 300, interval: 5, name: 'Refinery', size: 3, baseIncome: 300 }
            },
            entertainment: {
                park: { cost: 1000, income: 20, interval: 40, name: 'Park', size: 1, baseIncome: 20 },
                cinema: { cost: 3000, income: 60, interval: 25, name: 'Cinema', size: 2, baseIncome: 60 },
                stadium: { cost: 8000, income: 180, interval: 15, name: 'Stadium', size: 3, baseIncome: 180 }
            },
            education: {
                school: { cost: 1500, income: 30, interval: 35, name: 'School', size: 1, baseIncome: 30 },
                college: { cost: 4000, income: 90, interval: 20, name: 'College', size: 2, baseIncome: 90 },
                university: { cost: 9000, income: 250, interval: 10, name: 'University', size: 3, baseIncome: 250 }
            },
            nature: {
                garden: { cost: 800, income: 15, interval: 50, name: 'Garden', size: 1, baseIncome: 15 },
                forest: { cost: 2500, income: 50, interval: 30, name: 'Forest', size: 2, baseIncome: 50 },
                lake: { cost: 6000, income: 150, interval: 20, name: 'Lake', size: 3, baseIncome: 150 }
            }
        };

        // Game constants
        const GRID_SIZE = 20;
        const CELL_SIZE = 50;
        let selectedBuilding = null;
        let currentCategory = 'residential';
        let movingBuilding = null;

        // Initialize the game
        function initializeGame() {
            try {
                // Load game state
                if (!loadGameState()) {
                    showNotification('Starting new game...', 'info');
                }
                
                // Initialize UI
                updatePlayerStats();
                initializeTownGrid();
                updateBuildingList();
                renderTown();
                
                // Add event listeners
                setupEventListeners();
                
                // Start game loops
                startGameLoops();
                
                showNotification('Game initialized successfully!', 'success');
            } catch (e) {
                console.error('Error initializing game:', e);
                showNotification('Error initializing game!', 'error');
            }
        }

        // Setup event listeners
        function setupEventListeners() {
            // Category switching
            document.querySelectorAll('.category-item').forEach(item => {
                item.addEventListener('click', () => {
                    document.querySelector('.category-item.active').classList.remove('active');
                    item.classList.add('active');
                    currentCategory = item.dataset.category;
                    updateBuildingList();
                });
            });

            // Building selection
            document.getElementById('buildingsList').addEventListener('click', (event) => {
                const buildingItem = event.target.closest('.building-item');
                if (buildingItem) {
                    const buildingId = buildingItem.dataset.id;
                    selectBuilding(buildingId);
                }
            });

            // Grid cell clicks
            document.querySelectorAll('.grid-cell').forEach(cell => {
                cell.addEventListener('click', () => {
                    const x = parseInt(cell.dataset.x);
                    const y = parseInt(cell.dataset.y);
                    handleCellClick(x, y);
                });
            });

            // Context menu
            document.querySelector('.town-grid').addEventListener('contextmenu', (event) => {
                const building = event.target.closest('.building');
                if (building) {
                    event.preventDefault();
                    showBuildingContextMenu(event, building.dataset.id);
                }
            });

            // Game controls
            document.querySelector('.save-btn').addEventListener('click', saveGame);
            document.querySelector('.load-btn').addEventListener('click', loadGame);
            document.querySelector('.logout-btn').addEventListener('click', logout);
            document.querySelector('.back-btn').addEventListener('click', () => {
                window.location.href = 'index.html';
            });

            // Keyboard shortcuts
            document.addEventListener('keydown', (event) => {
                if (event.key === 'Escape') {
                    if (movingBuilding) {
                        movingBuilding = null;
                        document.querySelectorAll('.building-moving').forEach(cell => {
                            cell.classList.remove('building-moving');
                        });
                        showNotification('Building movement cancelled', 'info');
                    }
                }
            });

            // Window events
            window.addEventListener('resize', renderTown);
            window.addEventListener('beforeunload', saveGame);
        }

        // Start game loops
        function startGameLoops() {
            // Income generation
            setInterval(updateBuildings, 1000);
            
            // Auto-save
            setInterval(saveGame, 5 * 60 * 1000);
        }

        // Update player stats
        function updatePlayerStats() {
            document.getElementById('player-money').textContent = `$${gameState.money}`;
            document.getElementById('player-donuts').textContent = gameState.donuts;
            document.getElementById('player-name').textContent = localStorage.getItem('tsto_username') || 'Player';
        }

        // Initialize town grid
        function initializeTownGrid() {
            const grid = document.querySelector('.town-grid');
            grid.style.gridTemplateColumns = `repeat(${GRID_SIZE}, ${CELL_SIZE}px)`;
            grid.style.gridTemplateRows = `repeat(${GRID_SIZE}, ${CELL_SIZE}px)`;
            
            for (let y = 0; y < GRID_SIZE; y++) {
                for (let x = 0; x < GRID_SIZE; x++) {
                    const cell = document.createElement('div');
                    cell.className = 'grid-cell';
                    cell.dataset.x = x;
                    cell.dataset.y = y;
                    grid.appendChild(cell);
                }
            }
        }

        // Update building list
        function updateBuildingList() {
            const buildingsList = document.getElementById('buildingsList');
            buildingsList.innerHTML = '';
            
            Object.entries(BUILDINGS[currentCategory]).forEach(([id, building]) => {
                const buildingItem = document.createElement('div');
                buildingItem.className = 'building-item';
                buildingItem.dataset.id = id;
                buildingItem.innerHTML = `
                    <div class="building-preview" style="width: ${building.size * CELL_SIZE}px; height: ${building.size * CELL_SIZE}px;"></div>
                    <div class="building-info">
                        <span class="building-name">${building.name}</span>
                        <span class="building-cost">$${building.cost}</span>
                        <span class="building-income">$${building.income}/s</span>
                    </div>
                `;
                buildingsList.appendChild(buildingItem);
            });
        }

        // Select building
        function selectBuilding(buildingId) {
            const building = BUILDINGS[currentCategory][buildingId];
            if (gameState.money >= building.cost) {
                selectedBuilding = { id: buildingId, ...building };
                showNotification(`Selected ${building.name}. Click on the grid to place it.`, 'info');
            } else {
                showNotification('Not enough money!', 'error');
            }
        }

        // Handle cell click
        function handleCellClick(x, y) {
            if (movingBuilding) {
                moveBuildingTo(x, y);
            } else if (selectedBuilding) {
                placeBuilding(x, y);
            }
        }

        // Place building
        function placeBuilding(x, y) {
            if (!selectedBuilding) return;
            
            const building = selectedBuilding;
            if (gameState.money < building.cost) {
                showNotification('Not enough money!', 'error');
                return;
            }
            
            if (!canPlaceBuilding(x, y, building.size)) {
                showNotification('Cannot place building here!', 'error');
                return;
            }
            
            try {
                gameState.money -= building.cost;
                
                const buildingId = Date.now().toString();
                
                // Mark cells as occupied
                for (let i = 0; i < building.size; i++) {
                    for (let j = 0; j < building.size; j++) {
                        const cellX = x + i;
                        const cellY = y + j;
                        const cell = document.querySelector(`.grid-cell[data-x="${cellX}"][data-y="${cellY}"]`);
                        if (cell) {
                            cell.classList.add('occupied');
                            
                            if (i === 0 && j === 0) {
                                const buildingElement = document.createElement('div');
                                buildingElement.className = 'building';
                                buildingElement.dataset.id = buildingId;
                                buildingElement.style.width = `${building.size * CELL_SIZE}px`;
                                buildingElement.style.height = `${building.size * CELL_SIZE}px`;
                                buildingElement.innerHTML = `
                                    <div class="building-level">Level 1</div>
                                    <div class="building-income">$${building.income}/s</div>
                                    <div class="building-name">${building.name}</div>
                                    <div class="building-actions">
                                        <button class="upgrade-btn" onclick="upgradeBuilding('${buildingId}')">Upgrade ($${building.upgradeCost})</button>
                                    </div>
                                `;
                                cell.appendChild(buildingElement);
                            }
                        }
                    }
                }
                
                // Add building to game state
                gameState.buildings.push({
                    id: buildingId,
                    type: building.id,
                    category: currentCategory,
                    x: x,
                    y: y,
                    size: building.size,
                    lastCollection: Date.now(),
                    income: building.income,
                    baseIncome: building.baseIncome,
                    interval: building.interval,
                    level: 1,
                    name: building.name,
                    upgradeCost: Math.floor(building.cost * 1.5)
                });
                
                selectedBuilding = null;
                updatePlayerStats();
                saveGame();
                showNotification(`${building.name} placed successfully!`, 'success');
            } catch (e) {
                console.error('Error placing building:', e);
                showNotification('Error placing building!', 'error');
            }
        }

        // Move building
        function moveBuildingTo(x, y) {
            if (!movingBuilding) return;
            
            try {
                if (canPlaceBuilding(x, y, movingBuilding.size)) {
                    // Clear old position
                    for (let i = 0; i < movingBuilding.size; i++) {
                        for (let j = 0; j < movingBuilding.size; j++) {
                            const oldCellX = movingBuilding.x + i;
                            const oldCellY = movingBuilding.y + j;
                            const oldCell = document.querySelector(`.grid-cell[data-x="${oldCellX}"][data-y="${oldCellY}"]`);
                            if (oldCell) {
                                oldCell.classList.remove('occupied', 'building-moving');
                                oldCell.innerHTML = '';
                            }
                        }
                    }
                    
                    // Place in new position
                    for (let i = 0; i < movingBuilding.size; i++) {
                        for (let j = 0; j < movingBuilding.size; j++) {
                            const newCellX = x + i;
                            const newCellY = y + j;
                            const newCell = document.querySelector(`.grid-cell[data-x="${newCellX}"][data-y="${newCellY}"]`);
                            if (newCell) {
                                newCell.classList.add('occupied');
                                
                                if (i === 0 && j === 0) {
                                    const buildingElement = document.createElement('div');
                                    buildingElement.className = 'building';
                                    buildingElement.dataset.id = movingBuilding.id;
                                    buildingElement.style.width = `${movingBuilding.size * CELL_SIZE}px`;
                                    buildingElement.style.height = `${movingBuilding.size * CELL_SIZE}px`;
                                    buildingElement.innerHTML = `
                                        <div class="building-level">Level ${movingBuilding.level}</div>
                                        <div class="building-income">$${movingBuilding.income}/s</div>
                                        <div class="building-name">${movingBuilding.name}</div>
                                        <div class="building-actions">
                                            <button class="upgrade-btn" onclick="upgradeBuilding('${movingBuilding.id}')">Upgrade ($${movingBuilding.upgradeCost})</button>
                                        </div>
                                    `;
                                    newCell.appendChild(buildingElement);
                                }
                            }
                        }
                    }
                    
                    // Update building position in game state
                    movingBuilding.x = x;
                    movingBuilding.y = y;
                    
                    // Clear moving state
                    document.querySelectorAll('.building-moving').forEach(cell => {
                        cell.classList.remove('building-moving');
                    });
                    
                    showNotification('Building moved successfully!', 'success');
                    saveGame();
                } else {
                    showNotification('Cannot move building to that location!', 'error');
                }
            } catch (e) {
                console.error('Error moving building:', e);
                showNotification('Error moving building!', 'error');
            } finally {
                movingBuilding = null;
            }
        }

        // Check if building can be placed
        function canPlaceBuilding(x, y, size) {
            try {
                if (x < 0 || y < 0 || x + size > GRID_SIZE || y + size > GRID_SIZE) {
                    return false;
                }
                
                for (let i = 0; i < size; i++) {
                    for (let j = 0; j < size; j++) {
                        const cellX = x + i;
                        const cellY = y + j;
                        const cell = document.querySelector(`.grid-cell[data-x="${cellX}"][data-y="${cellY}"]`);
                        if (!cell || (cell.classList.contains('occupied') && !cell.classList.contains('building-moving'))) {
                            return false;
                        }
                    }
                }
                return true;
            } catch (e) {
                console.error('Error checking building placement:', e);
                return false;
            }
        }

        // Upgrade building
        function upgradeBuilding(buildingId) {
            const building = gameState.buildings.find(b => b.id === buildingId);
            if (!building) return;
            
            const upgradeCost = building.upgradeCost;
            if (gameState.money >= upgradeCost) {
                gameState.money -= upgradeCost;
                
                building.level++;
                const levelMultiplier = 1 + (building.level - 1) * 0.5;
                building.income = Math.floor(building.baseIncome * levelMultiplier);
                building.upgradeCost = Math.floor(upgradeCost * 1.5);
                
                const buildingElement = document.querySelector(`.building[data-id="${buildingId}"]`);
                if (buildingElement) {
                    buildingElement.querySelector('.building-level').textContent = `Level ${building.level}`;
                    buildingElement.querySelector('.building-income').textContent = `$${building.income}/s`;
                }
                
                updatePlayerStats();
                saveGame();
                showNotification(`${building.name} upgraded to level ${building.level}!`, 'success');
            } else {
                showNotification('Not enough money for upgrade!', 'error');
            }
        }

        // Show building context menu
        function showBuildingContextMenu(event, buildingId) {
            const building = gameState.buildings.find(b => b.id === buildingId);
            if (!building) return;
            
            const contextMenu = document.createElement('div');
            contextMenu.className = 'context-menu';
            contextMenu.innerHTML = `
                <div class="context-menu-item" onclick="upgradeBuilding('${buildingId}')">
                    Upgrade ($${building.upgradeCost})
                </div>
                <div class="context-menu-item" onclick="moveBuilding('${buildingId}')">
                    Move Building
                </div>
                <div class="context-menu-item danger" onclick="sellBuilding('${buildingId}')">
                    Sell Building
                </div>
            `;
            
            contextMenu.style.left = `${event.clientX}px`;
            contextMenu.style.top = `${event.clientY}px`;
            
            document.body.appendChild(contextMenu);
            
            document.addEventListener('click', function removeContextMenu() {
                contextMenu.remove();
                document.removeEventListener('click', removeContextMenu);
            });
        }

        // Move building
        function moveBuilding(buildingId) {
            const building = gameState.buildings.find(b => b.id === buildingId);
            if (!building) {
                showNotification('Building not found!', 'error');
                return;
            }

            movingBuilding = building;
            
            const cells = document.querySelectorAll('.grid-cell');
            cells.forEach(cell => {
                const x = parseInt(cell.dataset.x);
                const y = parseInt(cell.dataset.y);
                if (canPlaceBuilding(x, y, building.size)) {
                    cell.classList.add('building-moving');
                }
            });

            showNotification('Click on a valid location to move the building', 'info');
        }

        // Sell building
        function sellBuilding(buildingId) {
            const building = gameState.buildings.find(b => b.id === buildingId);
            if (!building) return;
            
            const sellValue = Math.floor(building.cost * 0.5 * building.level);
            gameState.money += sellValue;
            
            gameState.buildings = gameState.buildings.filter(b => b.id !== buildingId);
            
            for (let i = 0; i < building.size; i++) {
                for (let j = 0; j < building.size; j++) {
                    const cellX = building.x + i;
                    const cellY = building.y + j;
                    const cell = document.querySelector(`.grid-cell[data-x="${cellX}"][data-y="${cellY}"]`);
                    if (cell) {
                        cell.classList.remove('occupied');
                        cell.innerHTML = '';
                    }
                }
            }
            
            updatePlayerStats();
            saveGame();
            showNotification(`Sold ${building.name} for $${sellValue}!`, 'success');
        }

        // Update buildings
        function updateBuildings() {
            try {
                const now = Date.now();
                let totalIncome = 0;
                
                gameState.buildings.forEach(building => {
                    const timeSinceLastCollection = now - building.lastCollection;
                    const collectionInterval = building.interval * 1000;
                    
                    if (timeSinceLastCollection >= collectionInterval) {
                        const incomeMultiplier = timeSinceLastCollection / collectionInterval;
                        const income = Math.floor(building.income * incomeMultiplier);
                        
                        gameState.money += income;
                        totalIncome += income;
                        
                        showIncomeCollection(building, income);
                        
                        building.lastCollection = now;
                    }
                });
                
                if (totalIncome > 0) {
                    showNotification(`Collected $${totalIncome} from buildings!`, 'success');
                    updatePlayerStats();
                    saveGame();
                }
            } catch (e) {
                console.error('Error updating buildings:', e);
            }
        }

        // Show income collection
        function showIncomeCollection(building, amount) {
            const buildingElement = document.querySelector(`.building[data-id="${building.id}"]`);
            if (buildingElement) {
                const incomePopup = document.createElement('div');
                incomePopup.className = 'income-popup';
                incomePopup.textContent = `+$${amount}`;
                incomePopup.style.position = 'absolute';
                incomePopup.style.top = '0';
                incomePopup.style.left = '50%';
                incomePopup.style.transform = 'translateX(-50%)';
                incomePopup.style.animation = 'incomePopup 1s ease-out forwards';
                
                buildingElement.appendChild(incomePopup);
                
                setTimeout(() => {
                    incomePopup.remove();
                }, 1000);
            }
        }

        // Render town
        function renderTown() {
            document.querySelectorAll('.grid-cell').forEach(cell => {
                cell.classList.remove('occupied');
                cell.innerHTML = '';
            });
            
            gameState.buildings.forEach(building => {
                for (let i = 0; i < building.size; i++) {
                    for (let j = 0; j < building.size; j++) {
                        const cellX = building.x + i;
                        const cellY = building.y + j;
                        const cell = document.querySelector(`.grid-cell[data-x="${cellX}"][data-y="${cellY}"]`);
                        cell.classList.add('occupied');
                        
                        if (i === 0 && j === 0) {
                            const buildingElement = document.createElement('div');
                            buildingElement.className = 'building';
                            buildingElement.dataset.id = building.id;
                            buildingElement.style.width = `${building.size * CELL_SIZE}px`;
                            buildingElement.style.height = `${building.size * CELL_SIZE}px`;
                            buildingElement.innerHTML = `
                                <div class="building-level">Level ${building.level}</div>
                                <div class="building-income">$${building.income}/s</div>
                                <div class="building-name">${building.name}</div>
                                <div class="building-actions">
                                    <button class="upgrade-btn" onclick="upgradeBuilding('${building.id}')">Upgrade ($${building.upgradeCost})</button>
                                </div>
                            `;
                            cell.appendChild(buildingElement);
                        }
                    }
                }
            });
        }

        // Save game
        function saveGame() {
            try {
                const saveData = {
                    gameState: gameState,
                    currentUser: {
                        username: localStorage.getItem('tsto_username')
                    },
                    timestamp: Date.now()
                };
                localStorage.setItem('tsto_save', JSON.stringify(saveData));
                gameState.lastSave = Date.now();
                showNotification('Game saved successfully!', 'success');
                return true;
            } catch (e) {
                console.error('Error saving game:', e);
                showNotification('Error saving game!', 'error');
                return false;
            }
        }

        // Load game
        function loadGame() {
            if (loadGameState()) {
                showNotification('Game loaded successfully!', 'success');
            } else {
                showNotification('No valid save game found!', 'error');
            }
        }

        // Load game state
        function loadGameState() {
            try {
                const savedGame = localStorage.getItem('tsto_save');
                if (savedGame) {
                    const loadedData = JSON.parse(savedGame);
                    if (loadedData.gameState) {
                        if (typeof loadedData.gameState.money === 'number' &&
                            typeof loadedData.gameState.donuts === 'number' &&
                            Array.isArray(loadedData.gameState.buildings)) {
                            
                            loadedData.gameState.buildings = loadedData.gameState.buildings.filter(building => {
                                return building.id && building.type && building.x !== undefined && 
                                       building.y !== undefined && building.size && building.income;
                            });
                            
                            gameState = loadedData.gameState;
                            return true;
                        }
                    }
                }
            } catch (e) {
                console.error('Error loading game state:', e);
            }
            return false;
        }

        // Show notification
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        // Logout
        function logout() {
            localStorage.removeItem('tsto_username');
            window.location.href = 'index.html';
        }

        // Initialize the game when the page loads
        document.addEventListener('DOMContentLoaded', initializeGame);
    </script>
</body>
</html> 