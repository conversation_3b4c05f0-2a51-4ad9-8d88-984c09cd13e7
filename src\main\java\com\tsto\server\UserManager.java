package com.tsto.server;

import java.io.*;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;
import java.util.logging.Level;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import java.util.UUID;

public class UserManager {
    private static final Logger logger = Logger.getLogger(UserManager.class.getName());
    private static final String USERS_FILE = "data/users.dat";
    private final Map<String, User> users;
    private final ServerConfig config;
    private final DatabaseManager dbManager;

    public UserManager(ServerConfig config, DatabaseManager dbManager) {
        this.config = config;
        this.users = new HashMap<>();
        this.dbManager = dbManager;
        loadUsers();
    }

    private void loadUsers() {
        // Users are now loaded from the database
    }

    private void saveUsers() {
        File usersFile = new File(USERS_FILE);
        usersFile.getParentFile().mkdirs();

        try (ObjectOutputStream oos = new ObjectOutputStream(new FileOutputStream(usersFile))) {
            oos.writeObject(users);
            logger.info("Saved " + users.size() + " users");
        } catch (Exception e) {
            logger.log(Level.SEVERE, "Failed to save users", e);
        }
    }

    public User createUser(String username, String password) {
        String id = UUID.randomUUID().toString();
        String passwordHash = hashPassword(password);
        User user = new User(id, username, 1, 0, 100, 0);
        dbManager.createUser(id, username, passwordHash, 1, 0, 100, 0);
        return user;
    }

    public User authenticateUser(String username, String password) {
        for (User user : users.values()) {
            if (user.getUsername().equals(username) && user.checkPassword(password)) {
                user.updateLastLogin();
                saveUsers();
                return user;
            }
        }
        return null;
    }

    public User getUserById(String userId) {
        return users.get(userId);
    }

    public User getUserByUsername(String username) {
        for (User user : users.values()) {
            if (user.getUsername().equals(username)) {
                return user;
            }
        }
        return null;
    }

    public void updateUser(User user) {
        users.put(user.getUserId(), user);
        saveUsers();
    }

    public void deleteUser(String userId) {
        users.remove(userId);
        saveUsers();
    }

    private String generateUserId() {
        return Base64.getUrlEncoder().withoutPadding()
                .encodeToString(java.util.UUID.randomUUID().toString().getBytes());
    }

    private String hashPassword(String password) {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            byte[] hash = md.digest(password.getBytes());
            return Base64.getEncoder().encodeToString(hash);
        } catch (NoSuchAlgorithmException e) {
            logger.log(Level.SEVERE, "Error hashing password", e);
            throw new RuntimeException("Error hashing password", e);
        }
    }

    public boolean isUsernameTaken(String username) {
        return getUserByUsername(username) != null;
    }

    public int getUserCount() {
        return users.size();
    }
} 