package com.tsto.server;

public class QuestObject {
    private String id;
    private String name;
    private String type;
    private boolean isAvailable;
    private boolean isCompleted;
    private int rewardExperience;
    private int rewardMoney;

    public QuestObject(String id, String name, String type) {
        this.id = id;
        this.name = name;
        this.type = type;
        this.isAvailable = true;
        this.isCompleted = false;
        this.rewardExperience = 100; // Default values
        this.rewardMoney = 50;
    }

    public String getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public String getType() {
        return type;
    }

    public boolean isAvailable() {
        return isAvailable;
    }

    public void setAvailable(boolean available) {
        this.isAvailable = available;
    }

    public boolean isCompleted() {
        return isCompleted;
    }

    public void setCompleted(boolean completed) {
        this.isCompleted = completed;
    }

    public int getRewardExperience() {
        return rewardExperience;
    }

    public int getRewardMoney() {
        return rewardMoney;
    }
} 