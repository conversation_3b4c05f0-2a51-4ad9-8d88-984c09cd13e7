package com.tsto.server;

import java.util.logging.Logger;
import java.util.logging.Level;

public class Main {
    private static final Logger logger = Logger.getLogger(Main.class.getName());
    private static TSTOServer server;

    public static void main(String[] args) {
        // Configure logging
        configureLogging();
        
        logger.info("Starting server...");

        try {
            int port = 8080;
            server = new TSTOServer(port);
            server.start();
            
            // Add shutdown hook to properly close resources
            Runtime.getRuntime().addShutdownHook(new Thread(() -> {
                logger.info("Shutting down server...");
                if (server != null) {
                    server.stop();
                }
            }));
            
            logger.info("Server started on port " + port);
        } catch (Exception e) {
            logger.severe("Failed to start server: " + e.getMessage());
            System.exit(1);
        }
    }

    private static void configureLogging() {
        // Configure logging format and level
        System.setProperty("java.util.logging.SimpleFormatter.format",
                "[%1$tF %1$tT] [%4$-7s] %5$s %n");
        Logger rootLogger = Logger.getLogger("");
        rootLogger.setLevel(Level.FINE);
    }
} 