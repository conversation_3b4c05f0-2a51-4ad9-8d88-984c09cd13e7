package com.tsto.server;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Level;
import java.util.logging.Logger;

public class GameManager {
    private static final Logger logger = Logger.getLogger(GameManager.class.getName());
    private static GameManager instance;
    private final GameState gameState;
    private final Map<String, User> users;
    private final Map<String, Character> characters;
    private final Map<String, Building> buildings;
    private final Map<String, ShopItem> shopItems;
    private final Map<String, QuestObject> quests;
    private final Map<String, String> tokens;
    private final FriendSystem friendSystem;
    private final DatabaseManager dbManager;

    private GameManager() {
        this.gameState = GameState.getInstance();
        this.users = new ConcurrentHashMap<>();
        this.characters = new ConcurrentHashMap<>();
        this.buildings = new ConcurrentHashMap<>();
        this.shopItems = new ConcurrentHashMap<>();
        this.quests = new ConcurrentHashMap<>();
        this.tokens = new ConcurrentHashMap<>();
        this.friendSystem = FriendSystem.getInstance();
        this.dbManager = DatabaseManager.getInstance();
        initializeGameData();
    }

    private void initializeGameData() {
        // Initialize shop items
        shopItems.put("donut_shop", new ShopItem("donut_shop", "Donut Shop", "building", 1000, "A shop that sells donuts", "/images/donut_shop.png", false, 1));
        shopItems.put("kwik_e_mart", new ShopItem("kwik_e_mart", "Kwik-E-Mart", "building", 2000, "The famous convenience store", "/images/kwik_e_mart.png", false, 1));
        shopItems.put("power_plant", new ShopItem("power_plant", "Nuclear Power Plant", "building", 5000, "Springfield's power source", "/images/power_plant.png", false, 2));
        shopItems.put("statue", new ShopItem("statue", "Jebediah Statue", "decoration", 100, "Town founder's statue", "/images/statue.png", true, 1));

        // Initialize characters
        characters.put("homer", new Character("homer", "Homer Simpson"));
        characters.put("marge", new Character("marge", "Marge Simpson"));
        characters.put("bart", new Character("bart", "Bart Simpson"));
        characters.put("lisa", new Character("lisa", "Lisa Simpson"));

        // Initialize quests
        quests.put("welcome", new QuestObject("welcome", "Welcome to Springfield", "tutorial"));
        quests.put("kwik_e_mart", new QuestObject("kwik_e_mart", "Build a Kwik-E-Mart", "building"));
        quests.put("power_plant", new QuestObject("power_plant", "Work at the Power Plant", "character"));
    }

    public static synchronized GameManager getInstance() {
        if (instance == null) {
            instance = new GameManager();
        }
        return instance;
    }

    public User getUser(String userId) {
        return dbManager.getUser(userId);
    }

    public Map<String, ShopItem> getShopItems() {
        return Collections.unmodifiableMap(shopItems);
    }

    public Map<String, QuestObject> getQuests() {
        return Collections.unmodifiableMap(quests);
    }

    public Map<String, Character> getCharacters() {
        return Collections.unmodifiableMap(characters);
    }

    public User createUser(String username, String password) {
        try {
            String id = UUID.randomUUID().toString();
            String passwordHash = hashPassword(password);
            User user = new User(id, username, 1, 0, 100, 0);
            user.setPasswordHash(passwordHash);  // Set the password hash on the user object
            if (dbManager.createUser(id, username, passwordHash, 1, 0, 100, 0)) {
                logInfo("User created successfully: {0}", username);
                return user;
            }
            logWarning("Failed to create user: {0}", username);
            return null;
        } catch (Exception e) {
            handleError("Error creating user", e);
            return null;
        }
    }

    public User authenticateUser(String username, String password) {
        try {
            String passwordHash = hashPassword(password);
            User user = dbManager.authenticateUser(username, passwordHash);
            if (user != null) {
                logInfo("User authenticated successfully: {0}", username);
                return user;
            }
            logWarning("Authentication failed for user: {0}", username);
            return null;
        } catch (Exception e) {
            handleError("Error authenticating user", e);
            return null;
        }
    }

    public void logoutUser(String username) {
        tokens.remove(username);
        logInfo("User logged out: {0}", username);
    }

    public boolean purchaseItem(String userId, String itemId) {
        User user = dbManager.getUser(userId);
        ShopItem item = shopItems.get(itemId);
        
        if (user == null || item == null) {
            logWarning("Purchase failed: Invalid user or item");
            return false;
        }

        if (item.getCurrencyType().equals("money")) {
            if (user.getMoney() >= item.getPrice()) {
                user.spendMoney(item.getPrice());
                addItemToUserInventory(user, item);
                logInfo("Item purchased successfully: {0} by {1}", itemId, user.getUsername());
                return true;
            }
        } else if (item.getCurrencyType().equals("donuts")) {
            if (user.getDonuts() >= item.getPrice()) {
                user.spendDonuts(item.getPrice());
                addItemToUserInventory(user, item);
                logInfo("Item purchased successfully: {0} by {1}", itemId, user.getUsername());
                return true;
            }
        }
        
        logWarning("Purchase failed: User cannot afford item - {0}", itemId);
        return false;
    }

    public boolean startQuest(String userId, String questId) {
        User user = dbManager.getUser(userId);
        QuestObject quest = quests.get(questId);
        if (quest == null || !quest.isAvailable() || user == null) {
            logWarning("Quest start failed: Quest not available - {0}", questId);
            return false;
        }

        quest.setAvailable(false);
        logInfo("Quest started: {0} by {1}", questId, user.getUsername());
        return true;
    }

    public boolean completeQuest(String userId, String questId) {
        User user = dbManager.getUser(userId);
        QuestObject quest = quests.get(questId);
        if (quest == null || quest.isCompleted() || user == null) {
            logWarning("Quest completion failed: Quest not available or already completed - {0}", questId);
            return false;
        }

        quest.setCompleted(true);
        user.addExperience(quest.getRewardExperience());
        user.addMoney(quest.getRewardMoney());
        logInfo("Quest completed: {0} by {1}", questId, user.getUsername());
        return true;
    }

    public boolean assignTask(String userId, String characterId, String taskId) {
        try {
            User user = dbManager.getUser(userId);
            Character character = characters.get(characterId);

            if (user == null || character == null) {
                logWarning("Invalid user or character");
                return false;
            }

            if (character.isBusy()) {
                logWarning("Character is busy: {0}", character.getName());
                return false;
            }

            character.setCurrentTask(taskId, 3600); // Default 1-hour task
            logInfo("User {0} assigned task to {1}", user.getUsername(), character.getName());
            return true;
        } catch (Exception e) {
            handleError("Error assigning character task", e);
            return false;
        }
    }

    public boolean assignTask(Character character, String taskId, long duration) {
        if (character == null || character.isBusy()) {
            logWarning("Task assignment failed: Character is busy - {0}", character.getName());
            return false;
        }

        character.setCurrentTask(taskId, duration);
        logInfo("Task assigned: {0} to {1}", taskId, character.getName());
        return true;
    }

    public Set<String> getFriends(String username) {
        return friendSystem.getFriends(username);
    }

    public Set<String> getFriendRequests(String username) {
        return friendSystem.getPendingRequests(username);
    }

    private void addItemToUserInventory(User user, ShopItem item) {
        if (item.getCategory().equals("buildings")) {
            Building building = new Building(item.getId(), item.getName());
            buildings.put(building.getId(), building);
            user.addToInventory(item.getId(), item.getName(), "building");
        } else {
            user.addToInventory(item.getId(), item.getName(), item.getCategory());
        }
    }

    public void registerUser(String username, String token) {
        tokens.put(username, token);
        logInfo("Token registered for user: {0}", username);
    }

    public boolean validateToken(String token) {
        return token != null && tokens.containsValue(token);
    }

    public String getUsernameFromToken(String token) {
        for (Map.Entry<String, String> entry : tokens.entrySet()) {
            if (entry.getValue().equals(token)) {
                return entry.getKey();
            }
        }
        return null;
    }

    public boolean sendFriendRequest(String fromUsername, String toUsername) {
        if (!users.containsKey(fromUsername) || !users.containsKey(toUsername)) {
            logWarning("Friend request failed: Invalid usernames");
            return false;
        }
        return friendSystem.sendFriendRequest(fromUsername, toUsername);
    }

    public boolean acceptFriendRequest(String username, String friendUsername) {
        return friendSystem.acceptFriendRequest(username, friendUsername);
    }

    public boolean rejectFriendRequest(String username, String friendUsername) {
        return friendSystem.rejectFriendRequest(username, friendUsername);
    }

    public boolean removeFriend(String username, String friendUsername) {
        return friendSystem.removeFriend(username, friendUsername);
    }

    public boolean visitFriendTown(String username, String friendUsername) {
        return friendSystem.visitTown(username, friendUsername);
    }

    public Character getCharacter(String characterId) {
        return characters.get(characterId);
    }

    public Building getBuilding(String buildingId) {
        return buildings.get(buildingId);
    }

    private void assignTaskToCharacter(String characterId, String taskId) {
        Character character = characters.get(characterId);
        if (character == null) {
            logWarning("Character not found: {0}", characterId);
            return;
        }
        
        if (character.isBusy()) {
            logWarning("Character {0} is already busy", characterId);
            return;
        }
        
        try {
            character.setCurrentTask(taskId, System.currentTimeMillis());
            logInfo("Assigned task {0} to character {1}", taskId, characterId);
        } catch (Exception e) {
            handleError("Error assigning task to character", e);
        }
    }
    
    private void completeCharacterTask(String characterId) {
        Character character = characters.get(characterId);
        if (character == null) {
            logWarning("Character not found: {0}", characterId);
            return;
        }
        
        try {
            character.setCurrentTask(null, 0);
            logInfo("Completed task for character {0}", characterId);
        } catch (Exception e) {
            handleError("Error completing character task", e);
        }
    }
    
    public List<String> getFriendList(String userId) {
        try {
            Set<String> friends = friendSystem.getFriends(userId);
            return new ArrayList<>(friends);
        } catch (Exception e) {
            logWarning("Error getting friend list");
            return new ArrayList<>();
        }
    }

    private void handleError(String message, Exception e) {
        logger.log(Level.SEVERE, message, e);
    }
    
    private void logInfo(String message, Object... params) {
        logger.log(Level.INFO, message, params);
    }
    
    private void logWarning(String message, Object... params) {
        logger.log(Level.WARNING, message, params);
    }

    private String hashPassword(String password) {
        try {
            java.security.MessageDigest md = java.security.MessageDigest.getInstance("SHA-256");
            byte[] hash = md.digest(password.getBytes());
            return java.util.Base64.getEncoder().encodeToString(hash);
        } catch (java.security.NoSuchAlgorithmException e) {
            handleError("Error hashing password", e);
            return null;
        }
    }

    public void shutdown() {
        dbManager.close();
    }
} 