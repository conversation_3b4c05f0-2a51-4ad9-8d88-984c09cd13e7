package com.tsto.server;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.time.Instant;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;

public class User implements Serializable {
    private static final long serialVersionUID = 1L;
    
    private String id;
    private String username;
    private int level;
    private int experience;
    private int money;
    private int donuts;
    private String passwordHash;
    private Map<String, InventoryItem> inventory;
    private List<Achievement> achievements;
    private boolean isOnline;
    private long lastLoginTime;
    private String lastDirection;
    private DailyReward dailyReward;

    public User(String id, String username, int level, int experience, int money, int donuts) {
        this.id = id;
        this.username = username;
        this.level = level;
        this.experience = experience;
        this.money = money;
        this.donuts = donuts;
        this.inventory = new HashMap<>();
        this.achievements = new ArrayList<>();
        this.isOnline = false;
        this.lastLoginTime = Instant.now().getEpochSecond();
        this.lastDirection = "";
        this.dailyReward = new DailyReward();
    }

    // Getters and Setters
    public String getId() {
        return id;
    }

    public String getUserId() {
        return id;
    }

    public String getUsername() {
        return username;
    }

    public int getLevel() {
        return level;
    }

    public int getExperience() {
        return experience;
    }

    public int getMoney() {
        return money;
    }

    public int getDonuts() {
        return donuts;
    }

    public Map<String, InventoryItem> getInventory() {
        return new HashMap<>(inventory);
    }

    public List<Achievement> getAchievements() {
        return new ArrayList<>(achievements);
    }

    public long getLastLoginTime() {
        return lastLoginTime;
    }

    public boolean isOnline() {
        return isOnline;
    }

    public void setOnline(boolean online) {
        isOnline = online;
    }

    public void updateLastLogin() {
        this.lastLoginTime = Instant.now().getEpochSecond();
    }

    public void addExperience(int amount) {
        this.experience += amount;
        // Check for level up
        int experienceNeeded = calculateExperienceForNextLevel();
        while (this.experience >= experienceNeeded) {
            levelUp();
            experienceNeeded = calculateExperienceForNextLevel();
        }
    }

    private void levelUp() {
        this.level++;
        // Give rewards for leveling up
        this.money += 1000 * this.level;
        this.donuts += this.level;
    }

    private int calculateExperienceForNextLevel() {
        return 1000 * this.level;
    }

    public boolean addMoney(int amount) {
        if (amount < 0) return false;
        this.money += amount;
        return true;
    }

    public boolean spendMoney(int amount) {
        if (amount < 0 || this.money < amount) return false;
        this.money -= amount;
        return true;
    }

    public boolean addDonuts(int amount) {
        if (amount < 0) return false;
        this.donuts += amount;
        return true;
    }

    public boolean spendDonuts(int amount) {
        if (amount < 0 || this.donuts < amount) return false;
        this.donuts -= amount;
        return true;
    }

    public boolean addToInventory(String itemId, String itemName, String itemType) {
        InventoryItem item = inventory.get(itemId);
        if (item == null) {
            item = new InventoryItem(itemId, itemName, itemType);
            inventory.put(itemId, item);
        }
        item.setQuantity(item.getQuantity() + 1);
        return true;
    }

    public boolean removeFromInventory(String itemId) {
        InventoryItem item = inventory.get(itemId);
        if (item == null || item.getQuantity() <= 0) {
            return false;
        }
        
        item.setQuantity(item.getQuantity() - 1);
        if (item.getQuantity() == 0) {
            inventory.remove(itemId);
        }
        return true;
    }

    public void addAchievement(Achievement achievement) {
        if (!achievements.contains(achievement)) {
            achievements.add(achievement);
            // Grant experience reward for achievement
            addExperience(achievement.getExperienceReward());
        }
    }

    public boolean checkPassword(String password) {
        return this.passwordHash.equals(hashPassword(password));
    }

    private String hashPassword(String password) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(password.getBytes());
            return Base64.getEncoder().encodeToString(hash);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("Failed to hash password", e);
        }
    }

    public String getLastDirection() {
        return lastDirection;
    }

    public void setLastDirection(String lastDirection) {
        this.lastDirection = lastDirection;
    }

    public DailyReward getDailyReward() {
        return dailyReward;
    }

    public boolean claimDailyReward() {
        if (!dailyReward.canClaim()) {
            return false;
        }

        DailyReward.Reward reward = dailyReward.claim();
        if (reward != null) {
            addMoney(reward.getMoney());
            addDonuts(reward.getDonuts());
            addExperience(reward.getExperience());
            
            for (Map.Entry<String, Integer> item : reward.getItems().entrySet()) {
                for (int i = 0; i < item.getValue(); i++) {
                    addToInventory(item.getKey(), "Mystery Box", "special");
                }
            }
            return true;
        }
        return false;
    }

    public long getTimeUntilNextDailyReward() {
        return dailyReward.getTimeUntilNextClaim();
    }

    public int getCurrentDailyStreak() {
        return dailyReward.getCurrentStreak();
    }

    public DailyReward.Reward getNextDailyReward() {
        return dailyReward.getNextReward();
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public void setExperience(int experience) {
        this.experience = experience;
    }

    public void setMoney(int money) {
        this.money = money;
    }

    public void setDonuts(int donuts) {
        this.donuts = donuts;
    }

    public void setPasswordHash(String passwordHash) {
        this.passwordHash = passwordHash;
    }

    public String getPasswordHash() {
        return passwordHash;
    }
}