#!/usr/bin/env python3
"""
TSTO Web Game Debug Server
A specialized HTTP server for debugging The Simpsons: Tapped Out web game
with enhanced logging, request inspection, and response validation.
"""

import os
import sys
import json
import time
import logging
import traceback
import gzip
import io
from http.server import HTTPServer, SimpleHTTPRequestHandler
from urllib.parse import parse_qs, urlparse
from functools import lru_cache

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger('TSTODebugServer')

# Default user data for testing
DEFAULT_USER = {
    'userId': 'user123',
    'username': 'testuser',
    'level': 1,
    'experience': 0,
    'money': 1000,
    'donuts': 10
}

# In-memory storage for debugging
DEBUG_STORAGE = {
    'requests': [],
    'responses': [],
    'errors': [],
    'users': {
        'testuser': {
            'password': 'testpassword',
            'userId': 'user123',
            'level': 1,
            'experience': 0,
            'money': 1000,
            'donuts': 10,
            'game_state': {
                'money': 1000,
                'donuts': 10,
                'level': 1,
                'experience': 0,
                'buildings': [],
                'characters': [
                    {'name': 'Homer', 'status': 'idle'},
                    {'name': 'Marge', 'status': 'idle'}
                ]
            }
        }
    },
    'sessions': {}
}

# Cache for static files
FILE_CACHE = {}

class DebugRequestHandler(SimpleHTTPRequestHandler):
    """Enhanced request handler with debugging capabilities"""

    def __init__(self, *args, **kwargs):
        # Store the original working directory
        self.original_dir = os.getcwd()

        # Find the static directory
        self.static_dir = os.path.join(self.original_dir, "src", "main", "resources", "static")
        if not os.path.exists(self.static_dir):
            logger.warning(f"Static directory not found at: {self.static_dir}")
            # Try to find the static directory
            for root, dirs, _ in os.walk(self.original_dir):
                if "static" in dirs:
                    self.static_dir = os.path.join(root, "static")
                    logger.info(f"Found static directory at: {self.static_dir}")
                    break

        if os.path.exists(self.static_dir):
            logger.info(f"Serving files from: {self.static_dir}")

            # List files in the static directory
            logger.info("Files in the static directory:")
            for file in os.listdir(self.static_dir):
                logger.info(f"  - {file}")

                # Pre-cache common static files
                if file.endswith(('.html', '.js', '.css')):
                    file_path = os.path.join(self.static_dir, file)
                    try:
                        with open(file_path, 'rb') as f:
                            FILE_CACHE[file] = {
                                'content': f.read(),
                                'mtime': os.path.getmtime(file_path),
                                'content_type': self._get_content_type(file)
                            }
                        logger.debug(f"Pre-cached file: {file}")
                    except Exception as e:
                        logger.error(f"Error pre-caching file {file}: {str(e)}")
        else:
            logger.error(f"Could not find static directory")

        # Initialize the handler without changing directory
        super().__init__(*args, directory=self.static_dir, **kwargs)

    def _get_content_type(self, path):
        """Get content type based on file extension"""
        if path.endswith('.html'):
            return 'text/html'
        elif path.endswith('.js'):
            return 'application/javascript'
        elif path.endswith('.css'):
            return 'text/css'
        elif path.endswith('.json'):
            return 'application/json'
        elif path.endswith('.png'):
            return 'image/png'
        elif path.endswith('.jpg') or path.endswith('.jpeg'):
            return 'image/jpeg'
        elif path.endswith('.gif'):
            return 'image/gif'
        else:
            return 'application/octet-stream'

    def log_message(self, format, *args):
        """Override log_message to use our logger"""
        logger.info(f"{self.address_string()} - {format % args}")

    def log_request_details(self, method):
        """Log detailed request information"""
        request_id = len(DEBUG_STORAGE['requests']) + 1
        request_info = {
            'id': request_id,
            'timestamp': time.time(),
            'method': method,
            'path': self.path,
            'headers': dict(self.headers),
            'client_address': self.client_address[0]
        }

        logger.debug(f"Request #{request_id}: {method} {self.path}")
        logger.debug(f"Headers: {json.dumps(dict(self.headers), indent=2)}")

        DEBUG_STORAGE['requests'].append(request_info)
        return request_id

    def log_response_details(self, request_id, status_code, response_data):
        """Log detailed response information"""
        response_info = {
            'request_id': request_id,
            'timestamp': time.time(),
            'status_code': status_code,
            'response_data': response_data
        }

        logger.debug(f"Response for request #{request_id}: Status {status_code}")
        logger.debug(f"Response data: {json.dumps(response_data, indent=2)}")

        DEBUG_STORAGE['responses'].append(response_info)

    def log_error_details(self, request_id, error_type, error_message, traceback_info):
        """Log detailed error information"""
        error_info = {
            'request_id': request_id,
            'timestamp': time.time(),
            'error_type': error_type,
            'error_message': error_message,
            'traceback': traceback_info
        }

        logger.error(f"Error in request #{request_id}: {error_type} - {error_message}")
        logger.error(f"Traceback: {traceback_info}")

        DEBUG_STORAGE['errors'].append(error_info)

    def do_GET(self):
        """Handle GET requests with enhanced debugging and caching"""
        request_id = self.log_request_details('GET')

        try:
            # Special handling for API endpoints
            if self.path.startswith('/api/'):
                self.handle_api_get(request_id)
                return

            # Special handling for debug endpoints
            if self.path.startswith('/debug/'):
                self.handle_debug_get(request_id)
                return

            # Check if this is a static file request
            path = self.path.split('?')[0]  # Remove query parameters
            if path == '/':
                path = '/index.html'

            # Remove leading slash
            if path.startswith('/'):
                path = path[1:]

            # Check if file is in cache
            if path in FILE_CACHE:
                # Check if client has a cached version
                if_modified_since = self.headers.get('If-Modified-Since')
                if if_modified_since:
                    try:
                        client_mtime = time.mktime(time.strptime(if_modified_since, "%a, %d %b %Y %H:%M:%S GMT"))
                        if client_mtime >= FILE_CACHE[path]['mtime']:
                            # Client has the latest version
                            self.send_response(304)  # Not Modified
                            self.send_header('Cache-Control', 'max-age=86400')  # Cache for 1 day
                            self.end_headers()
                            return
                    except Exception as e:
                        logger.warning(f"Error parsing If-Modified-Since: {str(e)}")

                # Serve from cache
                self.send_response(200)
                self.send_header('Content-type', FILE_CACHE[path]['content_type'])
                self.send_header('Content-Length', str(len(FILE_CACHE[path]['content'])))
                self.send_header('Last-Modified', time.strftime("%a, %d %b %Y %H:%M:%S GMT",
                                                              time.gmtime(FILE_CACHE[path]['mtime'])))
                self.send_header('Cache-Control', 'max-age=86400')  # Cache for 1 day
                self.end_headers()
                self.wfile.write(FILE_CACHE[path]['content'])
                return

            # Default to serving static files
            return super().do_GET()
        except Exception as e:
            self.log_error_details(
                request_id,
                type(e).__name__,
                str(e),
                traceback.format_exc()
            )
            self.send_error(500, f"Internal Server Error: {str(e)}")

    def do_POST(self):
        """Handle POST requests with enhanced debugging"""
        request_id = self.log_request_details('POST')

        try:
            # Get request body
            content_length = int(self.headers.get('Content-Length', 0))
            if content_length > 0:
                post_data = self.rfile.read(content_length)
                try:
                    data = json.loads(post_data.decode('utf-8'))
                    logger.debug(f"Request #{request_id} POST data: {json.dumps(data, indent=2)}")
                except json.JSONDecodeError as e:
                    logger.error(f"Request #{request_id} Invalid JSON: {str(e)}")
                    self.send_error(400, "Invalid JSON")
                    return
            else:
                data = {}

            # Special handling for API endpoints
            if self.path.startswith('/api/'):
                self.handle_api_post(request_id, data)
                return

            # Special handling for debug endpoints
            if self.path.startswith('/debug/'):
                self.handle_debug_post(request_id, data)
                return

            # Default response for non-API POST requests
            self.send_error(404, "Not Found")
        except Exception as e:
            self.log_error_details(
                request_id,
                type(e).__name__,
                str(e),
                traceback.format_exc()
            )
            self.send_error(500, f"Internal Server Error: {str(e)}")

    def do_OPTIONS(self):
        """Handle OPTIONS requests for CORS"""
        request_id = self.log_request_details('OPTIONS')

        try:
            self.send_response(200)
            self.send_cors_headers()
            self.end_headers()

            self.log_response_details(request_id, 200, {"message": "CORS preflight request successful"})
        except Exception as e:
            self.log_error_details(
                request_id,
                type(e).__name__,
                str(e),
                traceback.format_exc()
            )
            self.send_error(500, f"Internal Server Error: {str(e)}")

    def handle_api_get(self, request_id):
        """Handle GET requests to API endpoints"""
        parts = self.path.split('/')
        endpoint = parts[2] if len(parts) > 2 else None

        if endpoint == 'gameState':
            # Return game state for the user
            query = urlparse(self.path).query
            params = parse_qs(query)
            user_id = params.get('userId', [''])[0]

            logger.info(f"Getting game state for user: {user_id}")

            # Find user by ID
            user_data = None
            for username, data in DEBUG_STORAGE['users'].items():
                if data['userId'] == user_id:
                    user_data = data
                    break

            if user_data:
                response_data = {
                    'success': True,
                    'gameState': user_data['game_state']
                }
            else:
                response_data = {
                    'success': False,
                    'message': f"User with ID {user_id} not found"
                }

            self.send_json_response(request_id, response_data)
        else:
            self.send_error(404, f"API endpoint not found: {endpoint}")
            self.log_response_details(request_id, 404, {"error": f"API endpoint not found: {endpoint}"})

    def handle_api_post(self, request_id, data):
        """Handle POST requests to API endpoints"""
        parts = self.path.split('/')
        endpoint = parts[2] if len(parts) > 2 else None

        if endpoint == 'login':
            self.handle_login(request_id, data)
        elif endpoint == 'register':
            self.handle_register(request_id, data)
        elif endpoint == 'save':
            self.handle_save_game(request_id, data)
        elif endpoint == 'load':
            self.handle_load_game(request_id, data)
        else:
            self.send_error(404, f"API endpoint not found: {endpoint}")
            self.log_response_details(request_id, 404, {"error": f"API endpoint not found: {endpoint}"})

    def handle_debug_get(self, request_id):
        """Handle GET requests to debug endpoints"""
        parts = self.path.split('/')
        endpoint = parts[2] if len(parts) > 2 else None

        if endpoint == 'requests':
            self.send_json_response(request_id, {"requests": DEBUG_STORAGE['requests']})
        elif endpoint == 'responses':
            self.send_json_response(request_id, {"responses": DEBUG_STORAGE['responses']})
        elif endpoint == 'errors':
            self.send_json_response(request_id, {"errors": DEBUG_STORAGE['errors']})
        elif endpoint == 'users':
            self.send_json_response(request_id, {"users": DEBUG_STORAGE['users']})
        elif endpoint == 'sessions':
            self.send_json_response(request_id, {"sessions": DEBUG_STORAGE['sessions']})
        elif endpoint == 'status':
            self.send_json_response(request_id, {
                "status": "running",
                "uptime": time.time() - server_start_time,
                "request_count": len(DEBUG_STORAGE['requests']),
                "error_count": len(DEBUG_STORAGE['errors']),
                "user_count": len(DEBUG_STORAGE['users']),
                "session_count": len(DEBUG_STORAGE['sessions'])
            })
        else:
            self.send_error(404, f"Debug endpoint not found: {endpoint}")
            self.log_response_details(request_id, 404, {"error": f"Debug endpoint not found: {endpoint}"})

    def handle_debug_post(self, request_id, data):
        """Handle POST requests to debug endpoints"""
        parts = self.path.split('/')
        endpoint = parts[2] if len(parts) > 2 else None

        if endpoint == 'reset':
            # Reset debug storage
            DEBUG_STORAGE['requests'] = []
            DEBUG_STORAGE['responses'] = []
            DEBUG_STORAGE['errors'] = []
            DEBUG_STORAGE['sessions'] = {}

            # Keep the test user
            test_user = DEBUG_STORAGE['users'].get('testuser')
            DEBUG_STORAGE['users'] = {'testuser': test_user} if test_user else {}

            self.send_json_response(request_id, {"message": "Debug storage reset successfully"})
        elif endpoint == 'add_user':
            # Add a test user
            username = data.get('username')
            password = data.get('password')

            if not username or not password:
                self.send_error(400, "Username and password are required")
                self.log_response_details(request_id, 400, {"error": "Username and password are required"})
                return

            user_id = f"user_{len(DEBUG_STORAGE['users']) + 1}"

            DEBUG_STORAGE['users'][username] = {
                'password': password,
                'userId': user_id,
                'level': 1,
                'experience': 0,
                'money': 1000,
                'donuts': 10,
                'game_state': {
                    'money': 1000,
                    'donuts': 10,
                    'level': 1,
                    'experience': 0,
                    'buildings': [],
                    'characters': [
                        {'name': 'Homer', 'status': 'idle'},
                        {'name': 'Marge', 'status': 'idle'}
                    ]
                }
            }

            self.send_json_response(request_id, {
                "message": f"User {username} added successfully",
                "userId": user_id
            })
        else:
            self.send_error(404, f"Debug endpoint not found: {endpoint}")
            self.log_response_details(request_id, 404, {"error": f"Debug endpoint not found: {endpoint}"})

    def handle_login(self, request_id, data):
        """Handle login requests with enhanced debugging"""
        username = data.get('username')
        password = data.get('password')

        logger.info(f"Login attempt: {username}")

        # Validate input
        if not username or not password:
            response_data = {
                'success': False,
                'message': 'Username and password are required'
            }
            self.send_json_response(request_id, response_data)
            return

        # Check if user exists
        user_data = DEBUG_STORAGE['users'].get(username)
        if not user_data:
            logger.warning(f"User not found: {username}")
            response_data = {
                'success': False,
                'message': 'Invalid username or password'
            }
            self.send_json_response(request_id, response_data)
            return

        # Check password
        if user_data['password'] != password:
            logger.warning(f"Invalid password for user: {username}")
            response_data = {
                'success': False,
                'message': 'Invalid username or password'
            }
            self.send_json_response(request_id, response_data)
            return

        # Generate session token
        session_token = f"session_{int(time.time())}_{username}"
        DEBUG_STORAGE['sessions'][session_token] = {
            'username': username,
            'userId': user_data['userId'],
            'created_at': time.time()
        }

        # Prepare response
        response_data = {
            'success': True,
            'userId': user_data['userId'],
            'username': username,
            'level': user_data['level'],
            'experience': user_data['experience'],
            'money': user_data['money'],
            'donuts': user_data['donuts'],
            'token': session_token,
            'message': 'Login successful',
            'game_state': user_data['game_state']
        }

        logger.info(f"Login successful for user: {username}")
        self.send_json_response(request_id, response_data)

    def handle_register(self, request_id, data):
        """Handle registration requests with enhanced debugging"""
        username = data.get('username')
        password = data.get('password')

        logger.info(f"Registration attempt: {username}")

        # Validate input
        if not username or not password:
            response_data = {
                'success': False,
                'message': 'Username and password are required'
            }
            self.send_json_response(request_id, response_data)
            return

        # Check if user already exists
        if username in DEBUG_STORAGE['users']:
            logger.warning(f"User already exists: {username}")
            response_data = {
                'success': False,
                'message': 'Username already taken'
            }
            self.send_json_response(request_id, response_data)
            return

        # Create new user
        user_id = f"user_{len(DEBUG_STORAGE['users']) + 1}"

        DEBUG_STORAGE['users'][username] = {
            'password': password,
            'userId': user_id,
            'level': 1,
            'experience': 0,
            'money': 1000,
            'donuts': 10,
            'game_state': {
                'money': 1000,
                'donuts': 10,
                'level': 1,
                'experience': 0,
                'buildings': [],
                'characters': [
                    {'name': 'Homer', 'status': 'idle'},
                    {'name': 'Marge', 'status': 'idle'}
                ]
            }
        }

        # Prepare response
        response_data = {
            'success': True,
            'userId': user_id,
            'username': username,
            'level': 1,
            'experience': 0,
            'money': 1000,
            'donuts': 10,
            'message': 'Registration successful'
        }

        logger.info(f"Registration successful for user: {username}")
        self.send_json_response(request_id, response_data)

    def handle_save_game(self, request_id, data):
        """Handle save game requests with enhanced debugging"""
        user_id = data.get('user_id')
        game_state = data.get('game_state')

        logger.info(f"Save game request for user ID: {user_id}")

        # Validate input
        if not user_id or not game_state:
            response_data = {
                'success': False,
                'message': 'User ID and game state are required'
            }
            self.send_json_response(request_id, response_data)
            return

        # Find user by ID
        user_found = False
        for username, user_data in DEBUG_STORAGE['users'].items():
            if user_data['userId'] == user_id:
                user_data['game_state'] = game_state
                user_found = True
                break

        if not user_found:
            logger.warning(f"User not found for ID: {user_id}")
            response_data = {
                'success': False,
                'message': f"User with ID {user_id} not found"
            }
            self.send_json_response(request_id, response_data)
            return

        # Prepare response
        response_data = {
            'success': True,
            'message': 'Game saved successfully'
        }

        logger.info(f"Game saved successfully for user ID: {user_id}")
        self.send_json_response(request_id, response_data)

    def handle_load_game(self, request_id, data):
        """Handle load game requests with enhanced debugging"""
        user_id = data.get('user_id')

        logger.info(f"Load game request for user ID: {user_id}")

        # Validate input
        if not user_id:
            response_data = {
                'success': False,
                'message': 'User ID is required'
            }
            self.send_json_response(request_id, response_data)
            return

        # Find user by ID
        user_data = None
        for username, data in DEBUG_STORAGE['users'].items():
            if data['userId'] == user_id:
                user_data = data
                break

        if not user_data:
            logger.warning(f"User not found for ID: {user_id}")
            response_data = {
                'success': False,
                'message': f"User with ID {user_id} not found"
            }
            self.send_json_response(request_id, response_data)
            return

        # Prepare response
        response_data = {
            'success': True,
            'game_state': user_data['game_state'],
            'message': 'Game loaded successfully'
        }

        logger.info(f"Game loaded successfully for user ID: {user_id}")
        self.send_json_response(request_id, response_data)

    def send_json_response(self, request_id, data):
        """Send a JSON response with enhanced debugging and compression"""
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_cors_headers()

        # Check if client accepts gzip encoding
        accept_encoding = self.headers.get('Accept-Encoding', '')
        use_gzip = 'gzip' in accept_encoding

        response_json = json.dumps(data)
        response_bytes = response_json.encode()

        if use_gzip and len(response_bytes) > 500:  # Only compress if response is large enough
            buffer = io.BytesIO()
            with gzip.GzipFile(fileobj=buffer, mode='wb') as f:
                f.write(response_bytes)
            response_bytes = buffer.getvalue()
            self.send_header('Content-Encoding', 'gzip')

        self.send_header('Content-Length', str(len(response_bytes)))
        self.send_header('Cache-Control', 'max-age=300')  # Cache for 5 minutes
        self.end_headers()

        self.wfile.write(response_bytes)
        self.log_response_details(request_id, 200, data)

    def send_cors_headers(self):
        """Send CORS headers"""
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')

def run_server(port=8081):
    """Run the debug server"""
    global server_start_time
    server_start_time = time.time()

    server_address = ('', port)
    httpd = HTTPServer(server_address, DebugRequestHandler)

    logger.info(f"Starting TSTO Debug Server on port {port}...")
    logger.info(f"Debug dashboard available at http://localhost:{port}/debug/status")
    logger.info(f"Press Ctrl+C to stop the server")

    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Server error: {str(e)}")
        logger.error(traceback.format_exc())

if __name__ == '__main__':
    port = 8081
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            logger.error(f"Invalid port number: {sys.argv[1]}")
            sys.exit(1)

    run_server(port)
