from http.server import HTTPServer, SimpleHTTPRequestHandler
import os
import sys
import json
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('TSTOServer')

class CustomHandler(SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        # Set the directory to serve files from
        static_dir = os.path.join(os.getcwd(), "src", "main", "resources", "static")
        logger.info(f"Setting directory to: {static_dir}")

        # Check if the directory exists
        if not os.path.exists(static_dir):
            logger.error(f"Directory does not exist: {static_dir}")
            # Try to find the static directory
            for root, dirs, files in os.walk(os.getcwd()):
                if "static" in dirs:
                    static_dir = os.path.join(root, "static")
                    logger.info(f"Found static directory at: {static_dir}")
                    break

        self.directory = static_dir
        super().__init__(*args, directory=self.directory, **kwargs)

    def do_GET(self):
        logger.info(f"GET request: {self.path}")
        return super().do_GET()

    def do_POST(self):
        logger.info(f"POST request: {self.path}")
        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length)
        data = json.loads(post_data.decode('utf-8'))
        logger.info(f"POST data: {data}")

        response = {
            'success': True,
            'message': 'Operation completed successfully'
        }

        if self.path == '/api/login':
            # Handle login
            username = data.get('username')
            password = data.get('password')
            logger.info(f"Login attempt: {username}")

            # For testing, accept any login
            response['userId'] = 'user123'
            response['username'] = username
            response['level'] = 1
            response['experience'] = 0
            response['money'] = 1000
            response['donuts'] = 10
            response['token'] = 'dummy_token'
            response['message'] = 'Login successful'

        elif self.path == '/api/register':
            # Handle registration
            username = data.get('username')
            password = data.get('password')
            logger.info(f"Registration attempt: {username}")

            # For testing, accept any registration
            response['userId'] = 'user123'
            response['username'] = username
            response['level'] = 1
            response['experience'] = 0
            response['money'] = 1000
            response['donuts'] = 10
            response['message'] = 'Registration successful'

        logger.info(f"Response: {response}")
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
        self.wfile.write(json.dumps(response).encode())

    def do_OPTIONS(self):
        logger.info(f"OPTIONS request: {self.path}")
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()

def run(port=8081):
    server_address = ('', port)
    httpd = HTTPServer(server_address, CustomHandler)
    logger.info(f'Starting server on port {port}...')
    logger.info(f'Current working directory: {os.getcwd()}')
    logger.info(f'Serving files from {os.path.join(os.getcwd(), "src", "main", "resources", "static")}')

    # List files in the static directory
    static_dir = os.path.join(os.getcwd(), "src", "main", "resources", "static")
    if os.path.exists(static_dir):
        logger.info(f"Files in {static_dir}:")
        for file in os.listdir(static_dir):
            logger.info(f"  - {file}")
    else:
        logger.error(f"Directory does not exist: {static_dir}")

    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Server error: {e}")

if __name__ == '__main__':
    port = 8081
    if len(sys.argv) > 1:
        port = int(sys.argv[1])
    run(port)
