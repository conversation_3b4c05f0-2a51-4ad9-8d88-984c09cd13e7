package com.tsto.server;

import com.sun.net.httpserver.HttpServer;
import com.sun.net.httpserver.HttpHandler;
import com.sun.net.httpserver.HttpExchange;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.InetSocketAddress;
import java.util.concurrent.Executors;
import java.util.logging.Logger;
import java.util.logging.Level;
import java.security.SecureRandom;
import java.util.Base64;
import java.nio.charset.StandardCharsets;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import java.net.BindException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

public class TSTOServer {
    private static final Logger LOGGER = Logger.getLogger(TSTOServer.class.getName());
    private HttpServer server;
    private String serverIp;
    private int serverPort;
    private boolean debug;
    private final SecureRandom secureRandom;
    private final GameManager gameManager;
    private final Gson gson;
    private final Path staticDir;

    public TSTOServer() {
        this.secureRandom = new SecureRandom();
        this.gameManager = GameManager.getInstance();
        this.gson = new Gson();
        this.debug = true;
        this.staticDir = Paths.get("src/main/resources/static");
    }

    public boolean initialize(int port) {
        try {
            this.serverPort = port;
            server = HttpServer.create(new InetSocketAddress(port), 0);
            server.setExecutor(Executors.newFixedThreadPool(10));

            // Register handlers
            server.createContext("/", new StaticFileHandler());
            server.createContext("/api/createUser", new CreateUserHandler());
            server.createContext("/api/purchase", new PurchaseHandler());
            server.createContext("/api/direction", new DirectionHandler());
            server.createContext("/api/lobbyTime", new LobbyTimeHandler());
            server.createContext("/api/friendData", new FriendDataHandler());
            server.createContext("/api/pluginEvent", new PluginEventHandler());

            LOGGER.info("TSTO server initialized on port " + port);
            return true;
        } catch (BindException e) {
            LOGGER.log(Level.SEVERE, "Port " + port + " is already in use. Please choose a different port or stop the process using it.");
            return false;
        } catch (IOException e) {
            LOGGER.log(Level.SEVERE, "Failed to initialize TSTO server on port " + port, e);
            return false;
        }
    }

    public void start() {
        if (server != null) {
            server.start();
            LOGGER.info("TSTO server started on port " + serverPort);
        } else {
            LOGGER.severe("Server not initialized. Call initialize() first.");
        }
    }

    public void stop() {
        if (server != null) {
            server.stop(0);
            LOGGER.info("TSTO server stopped");
        }
    }

    public String getServerAddress() {
        return serverPort == 80 ? serverIp : serverIp + ":" + serverPort;
    }

    public void setServerIp(String ip) {
        this.serverIp = ip;
    }

    public void setServerPort(int port) {
        this.serverPort = port;
    }

    protected String generateRandomId() {
        byte[] randomBytes = new byte[16];
        secureRandom.nextBytes(randomBytes);
        return Base64.getUrlEncoder().withoutPadding().encodeToString(randomBytes);
    }

    protected String generateSessionKey() {
        byte[] randomBytes = new byte[32];
        secureRandom.nextBytes(randomBytes);
        return Base64.getUrlEncoder().withoutPadding().encodeToString(randomBytes);
    }

    // Basic handler implementations
    private static class RootHandler implements HttpHandler {
        @Override
        public void handle(HttpExchange exchange) throws IOException {
            String response = "TSTO Private Server";
            exchange.sendResponseHeaders(200, response.length());
            exchange.getResponseBody().write(response.getBytes());
            exchange.getResponseBody().close();
        }
    }

    private static class DirectionHandler implements HttpHandler {
        @Override
        public void handle(HttpExchange exchange) throws IOException {
            sendResponse(exchange, 200, "{\"status\":\"ok\"}");
        }
    }

    private static class LobbyTimeHandler implements HttpHandler {
        @Override
        public void handle(HttpExchange exchange) throws IOException {
            // Implementation for lobby time endpoint
            // TODO: Implement lobby time logic
            exchange.sendResponseHeaders(200, 0);
            exchange.getResponseBody().close();
        }
    }

    private static class FriendDataHandler implements HttpHandler {
        @Override
        public void handle(HttpExchange exchange) throws IOException {
            // Implementation for friend data endpoint
            // TODO: Implement friend data logic
            exchange.sendResponseHeaders(200, 0);
            exchange.getResponseBody().close();
        }
    }

    private static class PluginEventHandler implements HttpHandler {
        @Override
        public void handle(HttpExchange exchange) throws IOException {
            // Implementation for plugin event endpoint
            // TODO: Implement plugin event logic
            exchange.sendResponseHeaders(200, 0);
            exchange.getResponseBody().close();
        }
    }

    private class CreateUserHandler implements HttpHandler {
        @Override
        public void handle(HttpExchange exchange) throws IOException {
            if (!"POST".equals(exchange.getRequestMethod())) {
                sendResponse(exchange, 405, "Method not allowed");
                return;
            }

            try {
                String requestBody = new String(exchange.getRequestBody().readAllBytes(), StandardCharsets.UTF_8);
                JsonObject json = gson.fromJson(requestBody, JsonObject.class);
                
                String username = json.get("username").getAsString();
                String password = json.get("password").getAsString();
                String userId = generateRandomId();
                
                if (gameManager.createUser(userId, username, password)) {
                    JsonObject response = new JsonObject();
                    response.addProperty("userId", userId);
                    response.addProperty("status", "success");
                    sendResponse(exchange, 200, response.toString());
                } else {
                    sendResponse(exchange, 400, "Failed to create user");
                }
            } catch (Exception e) {
                LOGGER.log(Level.SEVERE, "Error creating user", e);
                sendResponse(exchange, 500, "Internal server error");
            }
        }
    }

    private class PurchaseHandler implements HttpHandler {
        @Override
        public void handle(HttpExchange exchange) throws IOException {
            if (!"POST".equals(exchange.getRequestMethod())) {
                sendResponse(exchange, 405, "Method not allowed");
                return;
            }

            try {
                String requestBody = new String(exchange.getRequestBody().readAllBytes(), StandardCharsets.UTF_8);
                JsonObject json = gson.fromJson(requestBody, JsonObject.class);
                
                String userId = json.get("userId").getAsString();
                String itemId = json.get("itemId").getAsString();
                
                if (gameManager.purchaseItem(userId, itemId)) {
                    sendResponse(exchange, 200, "{\"status\":\"success\"}");
                } else {
                    sendResponse(exchange, 400, "{\"status\":\"failed\",\"message\":\"Purchase failed\"}");
                }
            } catch (Exception e) {
                LOGGER.log(Level.SEVERE, "Error processing purchase", e);
                sendResponse(exchange, 500, "Internal server error");
            }
        }
    }

    private static void sendResponse(HttpExchange exchange, int statusCode, String response) throws IOException {
        exchange.getResponseHeaders().set("Content-Type", "application/json");
        byte[] responseBytes = response.getBytes(StandardCharsets.UTF_8);
        exchange.sendResponseHeaders(statusCode, responseBytes.length);
        try (OutputStream os = exchange.getResponseBody()) {
            os.write(responseBytes);
        }
    }

    private class StaticFileHandler implements HttpHandler {
        @Override
        public void handle(HttpExchange exchange) throws IOException {
            String path = exchange.getRequestURI().getPath();
            LOGGER.info("Requested path: " + path);
            
            if (path.equals("/")) {
                path = "/index.html";
            }

            // Try to load from JAR first
            String resourcePath = "static" + path;
            LOGGER.info("Trying to load resource: " + resourcePath);
            InputStream resourceStream = getClass().getClassLoader().getResourceAsStream(resourcePath);
            
            if (resourceStream == null) {
                LOGGER.info("Resource not found in JAR, trying filesystem");
                // If not found in JAR, try filesystem
                Path filePath = staticDir.resolve(path.substring(1));
                LOGGER.info("Filesystem path: " + filePath);
                if (!Files.exists(filePath)) {
                    LOGGER.info("File not found in filesystem, serving index.html");
                    // If file not found, serve index.html
                    resourcePath = "static/index.html";
                    resourceStream = getClass().getClassLoader().getResourceAsStream(resourcePath);
                    if (resourceStream == null) {
                        LOGGER.severe("Could not find index.html");
                        sendResponse(exchange, 404, "File not found");
                        return;
                    }
                } else {
                    resourceStream = Files.newInputStream(filePath);
                }
            }

            String contentType = getContentType(path);
            LOGGER.info("Content type: " + contentType);
            exchange.getResponseHeaders().set("Content-Type", contentType);
            
            // Read the entire resource into memory to get its size
            byte[] content = resourceStream.readAllBytes();
            LOGGER.info("Content length: " + content.length);
            exchange.sendResponseHeaders(200, content.length);
            try (OutputStream os = exchange.getResponseBody()) {
                os.write(content);
            }
            resourceStream.close();
        }

        private String getContentType(String path) {
            path = path.toLowerCase();
            if (path.endsWith(".html")) return "text/html";
            if (path.endsWith(".css")) return "text/css";
            if (path.endsWith(".js")) return "application/javascript";
            if (path.endsWith(".png")) return "image/png";
            if (path.endsWith(".jpg") || path.endsWith(".jpeg")) return "image/jpeg";
            return "application/octet-stream";
        }
    }
} 