<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TSTO Web Game - WebGL Renderer</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            font-family: Arial, sans-serif;
            background-color: #7CFC00; /* Bright green fallback */
        }

        #game-container {
            position: relative;
            width: 100vw;
            height: 100vh;
            overflow: hidden;
            background-color: #7CFC00; /* Bright green fallback */
        }

        #game-canvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            cursor: grab;
            background-color: #7CFC00; /* Bright green fallback */
        }

        #ui-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        #player-info {
            position: absolute;
            top: 10px;
            left: 10px;
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 14px;
            pointer-events: auto;
        }

        #building-panel {
            position: absolute;
            bottom: 10px;
            left: 10px;
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 14px;
            pointer-events: auto;
            display: flex;
            flex-direction: row;
            gap: 10px;
            max-width: 80%;
            overflow-x: auto;
        }

        .building-button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 14px;
            margin: 4px 2px;
            cursor: pointer;
            border-radius: 5px;
        }

        #loading-screen {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            color: white;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            font-size: 24px;
            z-index: 1000;
        }

        #loading-progress {
            width: 300px;
            height: 20px;
            background-color: #333;
            border-radius: 10px;
            margin-top: 20px;
            overflow: hidden;
        }

        #loading-bar {
            width: 0%;
            height: 100%;
            background-color: #4CAF50;
            transition: width 0.3s ease-in-out;
        }

        /* Auth forms */
        #auth-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 2000;
        }

        .auth-form {
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            width: 300px;
            color: #333;
        }

        .auth-form h2 {
            margin-top: 0;
            text-align: center;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
        }

        .form-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }

        .auth-form button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }

        .auth-form button:hover {
            background-color: #45a049;
        }

        .auth-form button.secondary {
            background-color: #f44336;
        }

        .auth-form button.secondary:hover {
            background-color: #d32f2f;
        }
    </style>
</head>
<body>
    <div id="game-container">
        <canvas id="game-canvas"></canvas>

        <div id="ui-container">
            <div id="player-info">
                <div id="player-level">Level: 1</div>
                <div id="player-money">Money: $1000</div>
                <div id="player-donuts">Donuts: 10</div>
                <div style="margin-top: 10px;">
                    <button id="logout-btn" style="background-color: #f44336; color: white; border: none; padding: 5px 10px; border-radius: 4px; cursor: pointer;">Logout</button>
                </div>
            </div>

            <div id="building-panel">
                <button class="building-button" data-building="house">House</button>
                <button class="building-button" data-building="shop">Shop</button>
                <button class="building-button" data-building="factory">Factory</button>
                <button class="building-button" data-building="kwik_e_mart">Kwik-E-Mart</button>
                <button class="building-button" data-building="power_plant">Power Plant</button>
            </div>

            <!-- Character Panel -->
            <div id="character-panel" style="position: absolute; top: 10px; right: 10px; background-color: rgba(0, 0, 0, 0.7); color: white; padding: 10px; border-radius: 5px; pointer-events: auto; width: 200px;">
                <h3 style="margin-top: 0; text-align: center; color: #FFD700;">Characters</h3>
                <div id="character-list">
                    <div class="character-item" style="margin-bottom: 10px; padding: 10px; background-color: rgba(255, 215, 0, 0.3); border-radius: 5px; cursor: pointer;" onclick="showCharacterMenu('homer')">
                        <div style="display: flex; align-items: center;">
                            <div style="width: 40px; height: 40px; background-color: #FFD700; border-radius: 50%; display: flex; justify-content: center; align-items: center; font-weight: bold; margin-right: 10px;">H</div>
                            <div>
                                <div style="font-weight: bold;">Homer</div>
                                <div id="homer-status">Status: Idle</div>
                            </div>
                        </div>
                    </div>
                    <div class="character-item" style="margin-bottom: 10px; padding: 10px; background-color: rgba(30, 144, 255, 0.3); border-radius: 5px; cursor: pointer;" onclick="showCharacterMenu('marge')">
                        <div style="display: flex; align-items: center;">
                            <div style="width: 40px; height: 40px; background-color: #1E90FF; border-radius: 50%; display: flex; justify-content: center; align-items: center; font-weight: bold; margin-right: 10px;">M</div>
                            <div>
                                <div style="font-weight: bold;">Marge</div>
                                <div id="marge-status">Status: Idle</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div id="loading-screen">
            <div>Loading TSTO Web Game...</div>
            <div id="loading-progress">
                <div id="loading-bar"></div>
            </div>
            <div style="margin-top: 20px;">
                <button id="show-login-btn" style="background-color: #4CAF50; color: white; border: none; padding: 10px 15px; border-radius: 4px; cursor: pointer;">Show Login Form</button>
            </div>
        </div>

        <div id="auth-container">
            <div id="login-form" class="auth-form">
                <h2>Login</h2>
                <form id="login-form-element">
                    <div class="form-group">
                        <label for="login-username">Username:</label>
                        <input type="text" id="login-username" required>
                    </div>
                    <div class="form-group">
                        <label for="login-password">Password:</label>
                        <input type="password" id="login-password" required>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin-top: 15px;">
                        <button type="submit">Login</button>
                        <button type="button" onclick="showRegisterForm()">Register</button>
                    </div>
                </form>
                <div style="margin-top: 20px; text-align: center;">
                    <button type="button" id="add-test-user-btn" style="background-color: #ff9800; padding: 10px 15px; border: none; border-radius: 4px; cursor: pointer; width: 100%;">Add Test User & Auto-Login</button>
                </div>
            </div>

            <div id="register-form" class="auth-form" style="display: none;">
                <h2>Register</h2>
                <form id="register-form-element">
                    <div class="form-group">
                        <label for="register-username">Username:</label>
                        <input type="text" id="register-username" required>
                    </div>
                    <div class="form-group">
                        <label for="register-password">Password:</label>
                        <input type="password" id="register-password" required>
                    </div>
                    <button type="submit">Register</button>
                    <button type="button" onclick="showLoginForm()">Back to Login</button>
                </form>
            </div>
        </div>
    </div>

    <!-- Include WebGL renderer scripts -->
    <script src="webgl-renderer.js"></script>
    <script src="webgl-atlas.js"></script>
    <script src="webgl-sprites.js"></script>
    <script src="game-renderer.js"></script>

    <script>
        // Game state
        let gameState = {
            money: 1000,
            donuts: 10,
            level: 1,
            experience: 0,
            experienceToNextLevel: 100,
            buildings: [],
            characters: [],
            tasks: [],
            lastSave: Date.now(),
            lastUpdate: Date.now()
        };

        // User state
        let currentUser = null;

        // Game variables
        let gameRenderer = null;
        let selectedBuilding = null;
        let placingBuilding = false;
        let gameLoopInterval = null;

        // Building definitions
        const buildingDefinitions = {
            'house': {
                name: 'House',
                cost: 100,
                income: 10,
                incomeInterval: 60000, // 1 minute in milliseconds
                xp: 5,
                upgradeMultiplier: 1.5, // Cost and income multiplier per level
                maxLevel: 5,
                width: 2,
                height: 2,
                description: 'A simple house that generates a small income.'
            },
            'shop': {
                name: 'Shop',
                cost: 250,
                income: 25,
                incomeInterval: 120000, // 2 minutes
                xp: 15,
                upgradeMultiplier: 1.5,
                maxLevel: 5,
                width: 2,
                height: 2,
                description: 'A small shop that generates moderate income.'
            },
            'factory': {
                name: 'Factory',
                cost: 500,
                income: 50,
                incomeInterval: 180000, // 3 minutes
                xp: 30,
                upgradeMultiplier: 1.5,
                maxLevel: 5,
                width: 3,
                height: 3,
                description: 'A factory that generates good income.'
            },
            'kwik_e_mart': {
                name: 'Kwik-E-Mart',
                cost: 750,
                income: 75,
                incomeInterval: 240000, // 4 minutes
                xp: 50,
                upgradeMultiplier: 1.5,
                maxLevel: 5,
                width: 3,
                height: 2,
                description: 'The famous convenience store that generates great income.'
            },
            'power_plant': {
                name: 'Power Plant',
                cost: 1000,
                income: 100,
                incomeInterval: 300000, // 5 minutes
                xp: 75,
                upgradeMultiplier: 1.5,
                maxLevel: 5,
                width: 4,
                height: 4,
                description: 'A nuclear power plant that generates excellent income.'
            }
        };

        // Character definitions
        const characterDefinitions = {
            'homer': {
                name: 'Homer',
                tasks: {
                    'work_at_plant': {
                        name: 'Work at Power Plant',
                        duration: 240000, // 4 minutes
                        reward: 50,
                        xp: 20,
                        requiredBuilding: 'power_plant',
                        description: 'Homer works at the nuclear power plant.'
                    },
                    'eat_at_kwik_e_mart': {
                        name: 'Eat at Kwik-E-Mart',
                        duration: 120000, // 2 minutes
                        reward: 25,
                        xp: 10,
                        requiredBuilding: 'kwik_e_mart',
                        description: 'Homer buys donuts at the Kwik-E-Mart.'
                    }
                }
            },
            'marge': {
                name: 'Marge',
                tasks: {
                    'shop': {
                        name: 'Go Shopping',
                        duration: 180000, // 3 minutes
                        reward: 35,
                        xp: 15,
                        requiredBuilding: 'shop',
                        description: 'Marge goes shopping.'
                    },
                    'clean_house': {
                        name: 'Clean House',
                        duration: 120000, // 2 minutes
                        reward: 20,
                        xp: 10,
                        requiredBuilding: 'house',
                        description: 'Marge cleans the house.'
                    }
                }
            },
            'bart': {
                name: 'Bart',
                tasks: {
                    'prank_kwik_e_mart': {
                        name: 'Prank Kwik-E-Mart',
                        duration: 120000, // 2 minutes
                        reward: 15,
                        xp: 15,
                        requiredBuilding: 'kwik_e_mart',
                        description: 'Bart pranks Apu at the Kwik-E-Mart.'
                    },
                    'skateboard': {
                        name: 'Skateboard',
                        duration: 60000, // 1 minute
                        reward: 10,
                        xp: 5,
                        requiredBuilding: null, // No building required
                        description: 'Bart skateboards around town.'
                    }
                }
            }
        };

        // Simplified access to building costs
        const buildingCosts = {};
        Object.keys(buildingDefinitions).forEach(key => {
            buildingCosts[key] = buildingDefinitions[key].cost;
        });

        // Auth functions
        function showLoginForm() {
            document.getElementById('login-form').style.display = 'block';
            document.getElementById('register-form').style.display = 'none';
        }

        function showRegisterForm() {
            document.getElementById('login-form').style.display = 'none';
            document.getElementById('register-form').style.display = 'block';
        }

        function hideAuthForms() {
            document.getElementById('auth-container').style.display = 'none';
        }

        async function handleLogin(event) {
            event.preventDefault();
            console.log('Login form submitted');

            const username = document.getElementById('login-username').value;
            const password = document.getElementById('login-password').value;

            console.log(`Attempting to login user: ${username}`);

            try {
                // Use the correct endpoint from debug_server.py
                const response = await fetch('/api/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });

                console.log('Login response status:', response.status);
                const data = await response.json();
                console.log('Login response data:', data);

                if (data.success) {
                    currentUser = {
                        id: data.userId,
                        username: data.username,
                        level: data.level || 1,
                        money: data.money || 1000,
                        donuts: data.donuts || 10
                    };

                    // Update game state from server data
                    if (data.game_state) {
                        gameState = data.game_state;
                    }

                    // Store token in localStorage
                    localStorage.setItem('authToken', data.token);

                    // Hide auth forms
                    hideAuthForms();

                    // Update UI
                    updatePlayerStats();

                    // Show success notification
                    showNotification('Login successful!', true);
                    console.log('Login successful for', username);
                } else {
                    showNotification(data.message || 'Login failed. Please try again.', false);
                }
            } catch (error) {
                console.error('Login error:', error);
                showNotification('An error occurred during login. Please try again.', false);
            }
        }

        async function handleRegister(event) {
            event.preventDefault();
            console.log('Register form submitted');

            const username = document.getElementById('register-username').value;
            const password = document.getElementById('register-password').value;

            console.log(`Attempting to register user: ${username}`);

            try {
                // Use the correct endpoint from debug_server.py
                const response = await fetch('/api/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });

                console.log('Register response status:', response.status);
                const data = await response.json();
                console.log('Register response data:', data);

                if (data.success) {
                    showNotification('Registration successful! Please login.', true);
                    showLoginForm();
                } else {
                    showNotification(data.message || 'Registration failed. Username might be taken.', false);
                }
            } catch (error) {
                console.error('Registration error:', error);
                showNotification('An error occurred during registration. Please try again.', false);
            }
        }

        // Debug function to add a test user directly
        async function addTestUser() {
            try {
                console.log('Adding test user...');
                // Disable the button while processing
                const addTestUserBtn = document.getElementById('add-test-user-btn');
                if (addTestUserBtn) {
                    addTestUserBtn.disabled = true;
                    addTestUserBtn.textContent = 'Processing...';
                }

                const response = await fetch('/debug/add_user', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username: 'testuser', password: 'password' })
                });

                console.log('Add test user response status:', response.status);
                const data = await response.json();
                console.log('Add test user response:', data);

                // Don't show alert, just proceed to login
                console.log('Test user added: testuser/password');

                // Try to log in with the test user
                await testLogin();

                // Re-enable the button after processing
                if (addTestUserBtn) {
                    addTestUserBtn.disabled = false;
                    addTestUserBtn.textContent = 'Add Test User & Auto-Login';
                }
            } catch (error) {
                console.error('Error adding test user:', error);
                showNotification('Error adding test user: ' + error.message, false);

                // Re-enable the button if there was an error
                const addTestUserBtn = document.getElementById('add-test-user-btn');
                if (addTestUserBtn) {
                    addTestUserBtn.disabled = false;
                    addTestUserBtn.textContent = 'Add Test User & Auto-Login';
                }
            }
        }

        // Test login function
        async function testLogin() {
            try {
                console.log('Testing login with testuser...');
                const response = await fetch('/api/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username: 'testuser', password: 'password' })
                });

                console.log('Test login response status:', response.status);
                const data = await response.json();
                console.log('Test login response:', data);

                if (data.success) {
                    currentUser = {
                        id: data.userId,
                        username: data.username,
                        level: data.level || 1,
                        money: data.money || 1000,
                        donuts: data.donuts || 10
                    };

                    // Update game state from server data
                    if (data.game_state) {
                        gameState = data.game_state;
                    }

                    // Store token in localStorage
                    localStorage.setItem('authToken', data.token);

                    // Hide auth forms
                    hideAuthForms();

                    // Update UI
                    updatePlayerStats();

                    // Show success notification
                    showNotification('Login successful!', true);
                    console.log('Login successful for testuser');
                } else {
                    showNotification(data.message || 'Login failed. Please try again.', false);
                }
            } catch (error) {
                console.error('Test login error:', error);
                showNotification('Error during test login: ' + error.message, false);
            }
        }

        // Helper function to show notifications
        function showNotification(message, isSuccess = true) {
            const notification = document.createElement('div');
            notification.textContent = message;
            notification.style.position = 'fixed';
            notification.style.top = '20px';
            notification.style.left = '50%';
            notification.style.transform = 'translateX(-50%)';
            notification.style.backgroundColor = isSuccess ? 'rgba(76, 175, 80, 0.9)' : 'rgba(244, 67, 54, 0.9)';
            notification.style.color = 'white';
            notification.style.padding = '10px 20px';
            notification.style.borderRadius = '5px';
            notification.style.zIndex = '9999';
            document.body.appendChild(notification);

            // Remove the notification after 3 seconds
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 3000);
        }

        // Update player stats display
        function updatePlayerStats() {
            if (!currentUser) {
                console.log('No current user, cannot update stats');
                return;
            }

            console.log('Updating player stats for user:', currentUser);

            try {
                document.getElementById('player-level').textContent = `Level: ${currentUser.level}`;
                document.getElementById('player-money').textContent = `Money: $${currentUser.money}`;
                document.getElementById('player-donuts').textContent = `Donuts: ${currentUser.donuts}`;

                console.log('Player stats updated successfully');
            } catch (error) {
                console.error('Error updating player stats:', error);
            }
        }

        // Collect income from a building
        function collectIncome(buildingId) {
            const building = gameState.buildings.find(b => b.id === buildingId);
            if (!building) return;

            const now = Date.now();
            const timeSinceLastCollection = now - building.lastCollection;

            // Calculate how many full income intervals have passed
            const intervals = Math.floor(timeSinceLastCollection / building.incomeInterval);

            if (intervals > 0) {
                // Calculate income based on building level
                const income = building.income * building.level * intervals;

                // Add income to player's money
                gameState.money += income;

                // Add experience
                const xp = Math.floor(income / 10); // 10% of income as XP
                addExperience(xp);

                // Update last collection time
                building.lastCollection = now;

                // Update UI
                updatePlayerStats();

                // Show notification
                const buildingName = buildingDefinitions[building.type].name;
                showNotification(`Collected $${income} from ${buildingName}! +${xp} XP`, true);

                // Save game state
                saveGameState();

                return income;
            }

            return 0;
        }

        // Upgrade a building
        function upgradeBuilding(buildingId) {
            const building = gameState.buildings.find(b => b.id === buildingId);
            if (!building) return false;

            // Check if building is at max level
            if (building.level >= building.maxLevel) {
                showNotification(`${buildingDefinitions[building.type].name} is already at maximum level!`, false);
                return false;
            }

            // Calculate upgrade cost based on building level
            const baseCost = buildingDefinitions[building.type].cost;
            const upgradeCost = Math.floor(baseCost * Math.pow(building.upgradeMultiplier, building.level));

            // Check if player has enough money
            if (gameState.money < upgradeCost) {
                showNotification(`Not enough money! You need $${upgradeCost} to upgrade.`, false);
                return false;
            }

            // Deduct cost
            gameState.money -= upgradeCost;

            // Increase building level
            building.level++;

            // Increase income
            building.income = Math.floor(buildingDefinitions[building.type].income * Math.pow(building.upgradeMultiplier, building.level - 1));

            // Add experience
            const xp = buildingDefinitions[building.type].xp * building.level;
            addExperience(xp);

            // Update UI
            updatePlayerStats();

            // Show notification
            const buildingName = buildingDefinitions[building.type].name;
            showNotification(`${buildingName} upgraded to level ${building.level}! +${xp} XP`, true);

            // Save game state
            saveGameState();

            return true;
        }

        // Initialize the game
        async function initGame() {
            try {
                console.log('Initializing game...');

                // Set up auth event listeners
                try {
                    console.log('Setting up auth event listeners...');
                    const loginForm = document.getElementById('login-form-element');
                    const registerForm = document.getElementById('register-form-element');
                    const addTestUserBtn = document.getElementById('add-test-user-btn');
                    const showLoginBtn = document.getElementById('show-login-btn');

                    if (loginForm) {
                        console.log('Adding event listener to login form');
                        loginForm.addEventListener('submit', handleLogin);
                    } else {
                        console.error('Login form element not found');
                    }

                    if (registerForm) {
                        console.log('Adding event listener to register form');
                        registerForm.addEventListener('submit', handleRegister);
                    } else {
                        console.error('Register form element not found');
                    }

                    if (addTestUserBtn) {
                        console.log('Adding event listener to add test user button');
                        addTestUserBtn.addEventListener('click', addTestUser);
                    } else {
                        console.error('Add test user button not found');
                    }

                    if (showLoginBtn) {
                        console.log('Adding event listener to show login button');
                        showLoginBtn.addEventListener('click', () => {
                            // Show the login form
                            document.getElementById('auth-container').style.display = 'flex';
                            showLoginForm();

                            // Optionally hide the loading screen
                            document.getElementById('loading-screen').style.display = 'none';
                        });
                    } else {
                        console.error('Show login button not found');
                    }
                } catch (error) {
                    console.error('Error setting up auth event listeners:', error);
                }

                // Check if user is already logged in
                const token = localStorage.getItem('authToken');
                if (token) {
                    try {
                        console.log('Found auth token, validating...');
                        // Validate token with server
                        const response = await fetch('/api/validate', {
                            headers: {
                                'Authorization': `Bearer ${token}`
                            }
                        });

                        console.log('Token validation response status:', response.status);

                        if (response.ok) {
                            const userData = await response.json();
                            console.log('Token validation successful:', userData);
                            currentUser = userData;
                            hideAuthForms();
                        } else {
                            console.log('Token validation failed, showing login form');
                            // Invalid token, show login form
                            localStorage.removeItem('authToken');
                            showLoginForm();
                        }
                    } catch (error) {
                        console.error('Error validating token:', error);
                        showLoginForm();
                    }
                } else {
                    console.log('No auth token found, showing login form');
                    // No token, show login form
                    showLoginForm();
                }

                // Get the canvas
                const canvas = document.getElementById('game-canvas');

                // Create the game renderer
                gameRenderer = new GameRenderer(canvas);

                // Wait for the renderer to initialize
                await new Promise(resolve => {
                    // Check if the renderer is initialized every 100ms
                    const checkInterval = setInterval(() => {
                        if (gameRenderer.atlas && gameRenderer.atlas.regions) {
                            clearInterval(checkInterval);
                            resolve();
                        }
                    }, 100);

                    // Timeout after 10 seconds
                    setTimeout(() => {
                        clearInterval(checkInterval);
                        resolve();
                    }, 10000);
                });

                // Load game state
                loadGameState();

                // Set up game event listeners
                setupEventListeners();

                // Start the game loop
                startGameLoop();

                // Hide the loading screen
                document.getElementById('loading-screen').style.display = 'none';

                console.log('Game initialized successfully');
            } catch (e) {
                console.error('Error initializing game:', e);
            }
        }

        // Set up event listeners
        function setupEventListeners() {
            // Building buttons
            document.querySelectorAll('.building-button').forEach(button => {
                button.addEventListener('click', () => {
                    const buildingType = button.dataset.building;
                    selectBuilding(buildingType);
                });
            });

            // Canvas click for placing buildings
            document.getElementById('game-canvas').addEventListener('click', handleCanvasClick);

            // Logout button
            const logoutBtn = document.getElementById('logout-btn');
            if (logoutBtn) {
                logoutBtn.addEventListener('click', handleLogout);
            }
        }

        // Handle logout
        function handleLogout() {
            // Clear user data
            currentUser = null;

            // Clear auth token
            localStorage.removeItem('authToken');

            // Show login form
            document.getElementById('auth-container').style.display = 'flex';
            showLoginForm();

            // Show notification
            showNotification('Logged out successfully', true);
        }

        // Handle canvas click
        function handleCanvasClick(event) {
            // Get the click position in world coordinates
            const rect = event.target.getBoundingClientRect();
            const x = (event.clientX - rect.left) / gameRenderer.scale + gameRenderer.cameraX;
            const y = (event.clientY - rect.top) / gameRenderer.scale + gameRenderer.cameraY;

            console.log(`Canvas clicked at world coordinates: (${x}, ${y})`);

            if (placingBuilding && selectedBuilding) {
                // Place the building
                placeBuilding(selectedBuilding, Math.floor(x / 32) * 32, Math.floor(y / 32) * 32);

                // Reset the selection
                placingBuilding = false;
                selectedBuilding = null;
            } else {
                // Check if the player clicked on a building
                const clickedBuilding = findBuildingAtPosition(x, y);
                if (clickedBuilding) {
                    console.log(`Clicked on building: ${clickedBuilding.type} (${clickedBuilding.id})`);

                    // Collect income from the building
                    const income = collectIncome(clickedBuilding.id);

                    // Show building menu
                    showBuildingMenu(clickedBuilding);
                    return;
                }

                // Check if the player clicked on a character
                const clickedCharacter = findCharacterAtPosition(x, y);
                if (clickedCharacter) {
                    console.log(`Clicked on character: ${clickedCharacter.type} (${clickedCharacter.id})`);

                    // Show character menu
                    showCharacterMenuById(clickedCharacter.id);
                    return;
                }

                console.log('No building or character clicked');
            }
        }

        // Find a building at a specific position
        function findBuildingAtPosition(x, y) {
            for (const building of gameState.buildings) {
                const buildingWidth = buildingDefinitions[building.type].width * 32;
                const buildingHeight = buildingDefinitions[building.type].height * 32;

                if (x >= building.x && x < building.x + buildingWidth &&
                    y >= building.y && y < building.y + buildingHeight) {
                    return building;
                }
            }

            return null;
        }

        // Find a character at a specific position
        function findCharacterAtPosition(x, y) {
            // First check if we have any characters
            if (gameState.characters.length === 0) {
                console.log('No characters to find');
                return null;
            }

            console.log(`Looking for character at position (${x}, ${y})`);

            // Log all character positions for debugging
            gameState.characters.forEach(character => {
                console.log(`Character ${character.type} is at (${character.x}, ${character.y})`);
            });

            // Use a very large hit area for characters (128x128 pixels)
            const characterSize = 128;

            for (const character of gameState.characters) {
                // Add a much larger hit area around the character for easier clicking
                const hitAreaSize = characterSize * 2; // 100% larger than the character
                const hitX = character.x - (hitAreaSize - characterSize) / 2;
                const hitY = character.y - (hitAreaSize - characterSize) / 2;

                console.log(`Checking hit area for ${character.type}: (${hitX}, ${hitY}) to (${hitX + hitAreaSize}, ${hitY + hitAreaSize})`);

                if (x >= hitX && x < hitX + hitAreaSize &&
                    y >= hitY && y < hitY + hitAreaSize) {
                    console.log(`Found character ${character.type} at (${character.x}, ${character.y})`);

                    // Show a visual confirmation that the character was clicked
                    showClickConfirmation(x, y);

                    return character;
                }
            }

            // If no exact match, be more lenient and check if we're anywhere near a character
            for (const character of gameState.characters) {
                // Check a massive area around the character (300x300 pixels)
                const massiveHitArea = 300;
                const centerX = character.x + characterSize / 2;
                const centerY = character.y + characterSize / 2;

                // Calculate distance from click to character center
                const distance = Math.sqrt(Math.pow(x - centerX, 2) + Math.pow(y - centerY, 2));

                if (distance < massiveHitArea / 2) {
                    console.log(`Found character ${character.type} with lenient distance check (${distance} pixels away)`);

                    // Show a visual confirmation that the character was clicked
                    showClickConfirmation(x, y);

                    return character;
                }
            }

            console.log('No character found at this position');
            return null;
        }

        // Show a visual confirmation that a character was clicked
        function showClickConfirmation(x, y) {
            // Create a canvas for the confirmation effect
            const canvas = document.createElement('canvas');
            canvas.width = 100;
            canvas.height = 100;
            const ctx = canvas.getContext('2d');

            // Draw a starburst effect
            ctx.fillStyle = '#FFFF00';
            for (let i = 0; i < 8; i++) {
                ctx.beginPath();
                ctx.moveTo(50, 50);
                const angle1 = (i / 8) * Math.PI * 2;
                const angle2 = ((i + 0.5) / 8) * Math.PI * 2;
                ctx.lineTo(50 + Math.cos(angle1) * 50, 50 + Math.sin(angle1) * 50);
                ctx.lineTo(50 + Math.cos(angle2) * 25, 50 + Math.sin(angle2) * 25);
                ctx.closePath();
                ctx.fill();
            }

            // Add text
            ctx.fillStyle = '#FF0000';
            ctx.strokeStyle = '#000000';
            ctx.lineWidth = 2;
            ctx.font = 'bold 20px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.strokeText('CLICKED!', 50, 50);
            ctx.fillText('CLICKED!', 50, 50);

            // Add the confirmation to the atlas
            const confirmId = `click_confirm_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            gameRenderer.atlas.addImage(confirmId, canvas)
                .then(() => {
                    // Create a sprite for the confirmation
                    const sprite = gameRenderer.spriteManager.createSpriteFromAtlas(
                        confirmId,
                        gameRenderer.atlas,
                        confirmId,
                        x - 50,
                        y - 50,
                        {
                            width: 100,
                            height: 100,
                            zIndex: 3000 // Extremely high z-index
                        }
                    );

                    console.log(`Added click confirmation at (${x}, ${y})`);

                    // Animate the confirmation
                    let scale = 1.0;
                    let alpha = 1.0;

                    const interval = setInterval(() => {
                        scale += 0.05;
                        alpha -= 0.05;

                        sprite.scale = [scale, scale];
                        sprite.color = [1.0, 1.0, 0.0, alpha];

                        if (alpha <= 0) {
                            clearInterval(interval);
                            gameRenderer.spriteManager.removeSprite(sprite.id);
                        }
                    }, 50);
                })
                .catch(e => {
                    console.error(`Error adding click confirmation:`, e);
                });
        }

        // Show building menu
        function showBuildingMenu(building) {
            // Calculate upgrade cost
            const baseCost = buildingDefinitions[building.type].cost;
            const upgradeCost = Math.floor(baseCost * Math.pow(building.upgradeMultiplier, building.level));

            // Create a simple menu
            const menu = document.createElement('div');
            menu.style.position = 'absolute';
            menu.style.top = '50%';
            menu.style.left = '50%';
            menu.style.transform = 'translate(-50%, -50%)';
            menu.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
            menu.style.color = 'white';
            menu.style.padding = '20px';
            menu.style.borderRadius = '10px';
            menu.style.zIndex = '1000';
            menu.style.minWidth = '300px';

            // Add building info
            const buildingName = buildingDefinitions[building.type].name;
            menu.innerHTML = `
                <h3>${buildingName} (Level ${building.level})</h3>
                <p>Income: $${building.income} every ${building.incomeInterval / 1000} seconds</p>
                ${building.level < building.maxLevel ?
                    `<p>Upgrade Cost: $${upgradeCost}</p>
                    <button id="upgrade-btn" style="background-color: #4CAF50; color: white; border: none; padding: 10px; margin: 5px; cursor: pointer;">Upgrade to Level ${building.level + 1}</button>` :
                    '<p>Maximum level reached</p>'}
                <button id="close-btn" style="background-color: #f44336; color: white; border: none; padding: 10px; margin: 5px; cursor: pointer;">Close</button>
            `;

            document.body.appendChild(menu);

            // Add event listeners
            const upgradeBtn = document.getElementById('upgrade-btn');
            if (upgradeBtn) {
                upgradeBtn.addEventListener('click', () => {
                    upgradeBuilding(building.id);
                    document.body.removeChild(menu);
                });
            }

            const closeBtn = document.getElementById('close-btn');
            closeBtn.addEventListener('click', () => {
                document.body.removeChild(menu);
            });
        }

        // Show character menu by character type (from UI panel)
        function showCharacterMenu(characterType) {
            console.log(`Showing menu for character type: ${characterType}`);

            // Find the character in the game state
            const character = gameState.characters.find(c => c.type === characterType);

            if (!character) {
                console.error(`Character ${characterType} not found in game state`);
                showNotification(`Character ${characterType} not found!`, false);
                return;
            }

            showCharacterMenuById(character.id);
        }

        // Show character menu by character ID
        function showCharacterMenuById(characterId) {
            console.log(`Showing menu for character ID: ${characterId}`);

            // Find the character in the game state
            const character = gameState.characters.find(c => c.id === characterId);

            if (!character) {
                console.error(`Character with ID ${characterId} not found in game state`);
                showNotification(`Character not found!`, false);
                return;
            }

            // Create a simple menu
            const menu = document.createElement('div');
            menu.style.position = 'absolute';
            menu.style.top = '50%';
            menu.style.left = '50%';
            menu.style.transform = 'translate(-50%, -50%)';
            menu.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
            menu.style.color = 'white';
            menu.style.padding = '20px';
            menu.style.borderRadius = '10px';
            menu.style.zIndex = '1000';
            menu.style.minWidth = '300px';

            // Get character definition
            const charDef = characterDefinitions[character.type];

            // Add character info
            menu.innerHTML = `
                <h3>${charDef.name}</h3>
                <p>Status: ${character.status || 'Idle'}</p>
                <h4>Available Tasks:</h4>
                <div id="task-list"></div>
                <button id="close-btn" style="background-color: #f44336; color: white; border: none; padding: 10px; margin: 5px; cursor: pointer;">Close</button>
            `;

            document.body.appendChild(menu);

            // Add tasks
            const taskList = document.getElementById('task-list');

            // Check which tasks are available based on buildings
            for (const [taskId, task] of Object.entries(charDef.tasks)) {
                // Check if required building exists
                let canPerformTask = true;
                if (task.requiredBuilding) {
                    canPerformTask = gameState.buildings.some(b => b.type === task.requiredBuilding);
                }

                const taskElement = document.createElement('div');
                taskElement.style.margin = '10px 0';
                taskElement.style.padding = '10px';
                taskElement.style.backgroundColor = canPerformTask ? 'rgba(76, 175, 80, 0.3)' : 'rgba(244, 67, 54, 0.3)';
                taskElement.style.borderRadius = '5px';

                taskElement.innerHTML = `
                    <p><strong>${task.name}</strong></p>
                    <p>Duration: ${task.duration / 1000} seconds</p>
                    <p>Reward: $${task.reward}, XP: ${task.xp}</p>
                    <p>${task.description}</p>
                `;

                if (canPerformTask) {
                    const startButton = document.createElement('button');
                    startButton.textContent = 'Start Task';
                    startButton.style.backgroundColor = '#4CAF50';
                    startButton.style.color = 'white';
                    startButton.style.border = 'none';
                    startButton.style.padding = '5px 10px';
                    startButton.style.cursor = 'pointer';
                    startButton.style.borderRadius = '3px';

                    startButton.addEventListener('click', () => {
                        startCharacterTask(character.id, taskId);
                        document.body.removeChild(menu);
                    });

                    taskElement.appendChild(startButton);
                } else {
                    const requiredBuildingName = task.requiredBuilding ? buildingDefinitions[task.requiredBuilding].name : '';
                    taskElement.innerHTML += `<p style="color: #f44336;">Required: ${requiredBuildingName}</p>`;
                }

                taskList.appendChild(taskElement);
            }

            // Add event listener to close button
            const closeBtn = document.getElementById('close-btn');
            closeBtn.addEventListener('click', () => {
                document.body.removeChild(menu);
            });
        }

        // Select a building for placement
        function selectBuilding(buildingType) {
            // Check if the player has enough money
            if (gameState.money < buildingCosts[buildingType]) {
                alert(`Not enough money! You need $${buildingCosts[buildingType]}.`);
                return;
            }

            selectedBuilding = buildingType;
            placingBuilding = true;
        }

        // Place a building in the world
        function placeBuilding(buildingType, x, y) {
            const buildingDef = buildingDefinitions[buildingType];

            // Deduct the cost
            gameState.money -= buildingDef.cost;

            // Add experience for placing a building
            addExperience(buildingDef.xp);

            // Update UI
            updatePlayerStats();

            // Create a unique ID for the building
            const buildingId = `building_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

            // Add the building to the renderer
            const building = gameRenderer.addBuilding(buildingId, buildingType, x, y);

            // Add the building to the game state
            gameState.buildings.push({
                id: buildingId,
                type: buildingType,
                x: x,
                y: y,
                level: 1,
                income: buildingDef.income,
                incomeInterval: buildingDef.incomeInterval,
                lastCollection: Date.now(),
                upgradeMultiplier: buildingDef.upgradeMultiplier,
                maxLevel: buildingDef.maxLevel
            });

            // Show notification
            showNotification(`${buildingDef.name} placed! +${buildingDef.xp} XP`, true);

            // Save the game state
            saveGameState();

            // Check if any characters can perform tasks at this building
            checkAvailableTasks();
        }

        // Add experience points and handle leveling up
        function addExperience(amount) {
            gameState.experience += amount;

            // Check if player should level up
            while (gameState.experience >= gameState.experienceToNextLevel) {
                levelUp();
            }

            updatePlayerStats();
        }

        // Level up the player
        function levelUp() {
            // Subtract the required XP
            gameState.experience -= gameState.experienceToNextLevel;

            // Increase level
            gameState.level++;

            // Increase XP required for next level (gets harder each level)
            gameState.experienceToNextLevel = Math.floor(gameState.experienceToNextLevel * 1.5);

            // Give rewards for leveling up
            const moneyReward = gameState.level * 100;
            const donutReward = Math.floor(gameState.level / 5) + 1; // 1 donut at level 1-4, 2 at 5-9, etc.

            gameState.money += moneyReward;
            gameState.donuts += donutReward;

            // Show level up notification
            showNotification(`Level Up! You are now level ${gameState.level}! +$${moneyReward}, +${donutReward} donuts`, true);

            // Unlock new content based on level
            unlockContentForLevel(gameState.level);
        }

        // Unlock new content based on level
        function unlockContentForLevel(level) {
            // This function would unlock new buildings, characters, etc. based on level
            // For now, just show a notification
            if (level === 2) {
                showNotification('New building unlocked: Shop!', true);
            } else if (level === 3) {
                showNotification('New building unlocked: Factory!', true);
            } else if (level === 5) {
                showNotification('New character unlocked: Bart!', true);
            }
        }

        // Update player stats display
        function updatePlayerStats() {
            document.getElementById('player-level').textContent = `Level: ${gameState.level}`;
            document.getElementById('player-money').textContent = `Money: $${gameState.money}`;
            document.getElementById('player-donuts').textContent = `Donuts: ${gameState.donuts}`;

            // Add XP display
            const xpPercent = Math.min(100, Math.floor((gameState.experience / gameState.experienceToNextLevel) * 100));
            document.getElementById('player-level').title = `XP: ${gameState.experience}/${gameState.experienceToNextLevel} (${xpPercent}%)`;
        }

        // Start a character task
        function startCharacterTask(characterId, taskId) {
            console.log(`Starting task ${taskId} for character ${characterId}`);

            const character = gameState.characters.find(c => c.id === characterId);
            if (!character) {
                console.error(`Character with ID ${characterId} not found`);
                return false;
            }

            const charDef = characterDefinitions[character.type];
            const task = charDef.tasks[taskId];

            if (!task) {
                console.error(`Task ${taskId} not found for character ${character.type}`);
                return false;
            }

            // Check if required building exists
            if (task.requiredBuilding && !gameState.buildings.some(b => b.type === task.requiredBuilding)) {
                showNotification(`${charDef.name} needs a ${buildingDefinitions[task.requiredBuilding].name} to perform this task!`, false);
                return false;
            }

            // Set character status
            character.status = 'busy';

            // Add task to active tasks
            const taskInstance = {
                id: `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                characterId: characterId,
                taskId: taskId,
                startTime: Date.now(),
                endTime: Date.now() + task.duration,
                completed: false
            };

            gameState.tasks.push(taskInstance);

            // Update character sprite
            if (gameRenderer.characters[characterId]) {
                gameRenderer.characters[characterId].sprite.color = [0.7, 0.7, 0.7, 1.0]; // Gray out busy characters
            }

            // Update character status in UI panel
            updateCharacterStatusInUI(character.type, `Busy: ${task.name}`);

            // Show notification
            showNotification(`${charDef.name} started task: ${task.name}`, true);

            // Save game state
            saveGameState();

            return true;
        }

        // Update character status in the UI panel
        function updateCharacterStatusInUI(characterType, status) {
            console.log(`Updating UI status for ${characterType} to: ${status}`);

            const statusElement = document.getElementById(`${characterType}-status`);
            if (statusElement) {
                statusElement.textContent = `Status: ${status}`;

                // Also update the background color of the character item
                const characterItem = statusElement.closest('.character-item');
                if (characterItem) {
                    if (status.startsWith('Busy')) {
                        // Gray out the character item
                        characterItem.style.backgroundColor = 'rgba(128, 128, 128, 0.3)';
                    } else {
                        // Reset to original color
                        const color = characterType === 'homer' ? 'rgba(255, 215, 0, 0.3)' : 'rgba(30, 144, 255, 0.3)';
                        characterItem.style.backgroundColor = color;
                    }
                }
            } else {
                console.error(`Status element for ${characterType} not found`);
            }
        }

        // Complete a character task
        function completeCharacterTask(taskInstance) {
            console.log(`Completing task ${taskInstance.taskId} for character ${taskInstance.characterId}`);

            const character = gameState.characters.find(c => c.id === taskInstance.characterId);
            if (!character) {
                console.error(`Character with ID ${taskInstance.characterId} not found`);
                return false;
            }

            const charDef = characterDefinitions[character.type];
            const task = charDef.tasks[taskInstance.taskId];

            if (!task) {
                console.error(`Task ${taskInstance.taskId} not found for character ${character.type}`);
                return false;
            }

            // Mark task as completed
            taskInstance.completed = true;

            // Set character status back to idle
            character.status = 'idle';

            // Give rewards
            gameState.money += task.reward;
            addExperience(task.xp);

            // Update character sprite
            if (gameRenderer.characters[character.id]) {
                gameRenderer.characters[character.id].sprite.color = [1.0, 1.0, 1.0, 1.0]; // Reset color
            }

            // Update character status in UI panel
            updateCharacterStatusInUI(character.type, 'Idle');

            // Show notification
            showNotification(`${charDef.name} completed task: ${task.name}! +$${task.reward}, +${task.xp} XP`, true);

            // Update UI
            updatePlayerStats();

            // Save game state
            saveGameState();

            return true;
        }

        // Check for available tasks based on buildings
        function checkAvailableTasks() {
            // This function would check which tasks are available based on buildings
            // and notify the player if new tasks are available

            // For now, just check if any characters can perform tasks at any buildings
            for (const character of gameState.characters) {
                const charDef = characterDefinitions[character.type];
                if (!charDef) continue;

                for (const [taskId, task] of Object.entries(charDef.tasks)) {
                    if (task.requiredBuilding && gameState.buildings.some(b => b.type === task.requiredBuilding)) {
                        // Character can perform this task
                        console.log(`${charDef.name} can perform task: ${task.name}`);
                    }
                }
            }
        }

        // Start the game loop
        function startGameLoop() {
            // Clear any existing interval
            if (gameLoopInterval) {
                clearInterval(gameLoopInterval);
            }

            // Set up the game loop to run every second
            gameLoopInterval = setInterval(gameLoop, 1000);
            console.log('Game loop started');
        }

        // The main game loop
        function gameLoop() {
            const now = Date.now();
            const timeSinceLastUpdate = now - gameState.lastUpdate;

            // Update tasks
            updateTasks(now);

            // Auto-collect income from buildings if enough time has passed
            if (timeSinceLastUpdate > 60000) { // Every minute
                autoCollectIncome();
                gameState.lastUpdate = now;
            }

            // Save game state every 5 minutes
            if (now - gameState.lastSave > 300000) {
                saveGameState();
            }
        }

        // Update tasks in the game loop
        function updateTasks(now) {
            // Check for completed tasks
            for (const task of gameState.tasks) {
                if (!task.completed && now >= task.endTime) {
                    completeCharacterTask(task);
                }
            }

            // Remove completed tasks that are older than 1 hour
            gameState.tasks = gameState.tasks.filter(task => {
                return !task.completed || (now - task.endTime < 3600000);
            });
        }

        // Auto-collect income from buildings
        function autoCollectIncome() {
            let totalIncome = 0;
            let totalXp = 0;

            for (const building of gameState.buildings) {
                const income = collectIncome(building.id);
                if (income > 0) {
                    totalIncome += income;
                    totalXp += Math.floor(income / 10); // 10% of income as XP
                }
            }

            if (totalIncome > 0) {
                showNotification(`Auto-collected $${totalIncome} from all buildings! +${totalXp} XP`, true);
            }
        }

        // Load game state from local storage
        function loadGameState() {
            try {
                const savedState = localStorage.getItem('tstoGameState');
                if (savedState) {
                    gameState = JSON.parse(savedState);

                    // Ensure all required properties exist
                    gameState.experienceToNextLevel = gameState.experienceToNextLevel || 100;
                    gameState.tasks = gameState.tasks || [];
                    gameState.lastUpdate = gameState.lastUpdate || Date.now();

                    // Load buildings
                    gameState.buildings.forEach(building => {
                        gameRenderer.addBuilding(building.id, building.type, building.x, building.y, {
                            level: building.level
                        });
                    });

                    // Load characters
                    gameState.characters.forEach(character => {
                        // Add visual indicator for busy characters
                        const color = character.status === 'busy' ? [0.7, 0.7, 0.7, 1.0] : [1.0, 1.0, 1.0, 1.0];

                        console.log(`Loading character from saved state: ${character.type} at (${character.x}, ${character.y})`);

                        // Position characters in the visible area if they're at default positions
                        if (character.x === -50 || character.x === 50) {
                            const centerX = gameRenderer.cameraX + (gameRenderer.canvas.width / gameRenderer.scale / 2);
                            const centerY = gameRenderer.cameraY + (gameRenderer.canvas.height / gameRenderer.scale / 2);

                            if (character.type === 'homer') {
                                character.x = centerX - 50;
                                character.y = centerY;
                            } else if (character.type === 'marge') {
                                character.x = centerX + 50;
                                character.y = centerY;
                            }

                            console.log(`Repositioned ${character.type} to (${character.x}, ${character.y})`);
                        }

                        const characterObj = gameRenderer.addCharacter(character.id, character.type, character.x, character.y, {
                            status: character.status,
                            color: color,
                            width: 64,  // Make characters bigger
                            height: 64,
                            zIndex: 100 // Make sure they're on top
                        });

                        if (characterObj) {
                            console.log(`Character ${character.type} loaded successfully:`, characterObj);
                        } else {
                            console.error(`Failed to load character ${character.type}`);
                        }
                    });

                    // Add visual indicators for character positions
                    addCharacterIndicators();

                    // Add default characters if none exist
                    if (gameState.characters.length === 0) {
                        addDefaultCharacters();
                    }

                    updatePlayerStats();
                } else {
                    // Add default characters for new game
                    addDefaultCharacters();
                }
            } catch (e) {
                console.error('Error loading game state:', e);
                // Add default characters if there was an error
                addDefaultCharacters();
            }
        }

        // Add default characters to the game
        function addDefaultCharacters() {
            // Add Homer and Marge as starting characters
            // Position them in the center of the screen for better visibility
            const centerX = gameRenderer.cameraX + (gameRenderer.canvas.width / gameRenderer.scale / 2);
            const centerY = gameRenderer.cameraY + (gameRenderer.canvas.height / gameRenderer.scale / 2);

            const homerX = centerX - 100;
            const homerY = centerY;
            const homerId = `character_homer_${Date.now()}`;

            console.log(`Adding Homer at position (${homerX}, ${homerY})`);

            // Create a large, unmistakable character sprite for Homer
            createLargeCharacterSprite('homer', homerX, homerY, '#FFD700');

            gameState.characters.push({
                id: homerId,
                type: 'homer',
                x: homerX,
                y: homerY,
                status: 'idle'
            });

            const margeX = centerX + 100;
            const margeY = centerY;
            const margeId = `character_marge_${Date.now()}`;

            console.log(`Adding Marge at position (${margeX}, ${margeY})`);

            // Create a large, unmistakable character sprite for Marge
            createLargeCharacterSprite('marge', margeX, margeY, '#1E90FF');

            gameState.characters.push({
                id: margeId,
                type: 'marge',
                x: margeX,
                y: margeY,
                status: 'idle'
            });

            showNotification('Homer and Marge are ready to help build your town!', true);

            // Create a floating arrow pointing to Homer
            createFloatingArrow(homerX, homerY - 100, 'Click on Homer!');

            // Create a floating arrow pointing to Marge
            createFloatingArrow(margeX, margeY - 100, 'Click on Marge!');
        }

        // Create a large, unmistakable character sprite
        function createLargeCharacterSprite(characterType, x, y, color) {
            // Create a canvas for the character
            const canvas = document.createElement('canvas');
            canvas.width = 128;
            canvas.height = 128;
            const ctx = canvas.getContext('2d');

            // Draw a colored background
            ctx.fillStyle = color;
            ctx.fillRect(0, 0, 128, 128);

            // Draw a border
            ctx.strokeStyle = '#000000';
            ctx.lineWidth = 4;
            ctx.strokeRect(0, 0, 128, 128);

            // Add character name
            ctx.fillStyle = '#000000';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(characterType.toUpperCase(), 64, 40);

            // Add "CLICK ME" text
            ctx.fillStyle = '#FF0000';
            ctx.font = 'bold 20px Arial';
            ctx.fillText('CLICK ME', 64, 80);

            // Add the character to the atlas
            const spriteId = `large_${characterType}_${Date.now()}`;
            gameRenderer.atlas.addImage(spriteId, canvas)
                .then(() => {
                    // Create a sprite for the character
                    const sprite = gameRenderer.spriteManager.createSpriteFromAtlas(
                        spriteId,
                        gameRenderer.atlas,
                        spriteId,
                        x,
                        y,
                        {
                            width: 128,
                            height: 128,
                            zIndex: 1000 // Very high z-index to ensure visibility
                        }
                    );

                    console.log(`Added large sprite for ${characterType} at (${x}, ${y})`);

                    // Add animation to make it more noticeable
                    animateCharacterSprite(sprite);
                })
                .catch(e => {
                    console.error(`Error adding large sprite for ${characterType}:`, e);
                });
        }

        // Create a floating arrow pointing to a character
        function createFloatingArrow(x, y, text) {
            // Create a canvas for the arrow
            const canvas = document.createElement('canvas');
            canvas.width = 200;
            canvas.height = 100;
            const ctx = canvas.getContext('2d');

            // Draw arrow
            ctx.fillStyle = '#FF0000';
            ctx.beginPath();
            ctx.moveTo(100, 80);  // Arrow tip
            ctx.lineTo(80, 60);   // Left wing
            ctx.lineTo(90, 60);   // Left indent
            ctx.lineTo(90, 20);   // Left side of shaft
            ctx.lineTo(110, 20);  // Top of shaft
            ctx.lineTo(110, 60);  // Right side of shaft
            ctx.lineTo(120, 60);  // Right indent
            ctx.closePath();
            ctx.fill();

            // Add text
            ctx.fillStyle = '#FFFFFF';
            ctx.strokeStyle = '#000000';
            ctx.lineWidth = 2;
            ctx.font = 'bold 18px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.strokeText(text, 100, 40);
            ctx.fillText(text, 100, 40);

            // Add the arrow to the atlas
            const arrowId = `arrow_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            gameRenderer.atlas.addImage(arrowId, canvas)
                .then(() => {
                    // Create a sprite for the arrow
                    const sprite = gameRenderer.spriteManager.createSpriteFromAtlas(
                        arrowId,
                        gameRenderer.atlas,
                        arrowId,
                        x - 100,
                        y,
                        {
                            width: 200,
                            height: 100,
                            zIndex: 2000 // Extremely high z-index
                        }
                    );

                    console.log(`Added floating arrow at (${x}, ${y})`);

                    // Animate the arrow
                    animateArrow(sprite);
                })
                .catch(e => {
                    console.error(`Error adding floating arrow:`, e);
                });
        }

        // Animate a character sprite to make it more noticeable
        function animateCharacterSprite(sprite) {
            let scale = 1.0;
            let growing = true;

            // Pulse animation
            setInterval(() => {
                if (growing) {
                    scale += 0.01;
                    if (scale >= 1.1) growing = false;
                } else {
                    scale -= 0.01;
                    if (scale <= 0.9) growing = true;
                }

                sprite.scale = [scale, scale];
            }, 50);
        }

        // Animate an arrow to make it more noticeable
        function animateArrow(sprite) {
            let yOffset = 0;
            let goingUp = true;

            // Bouncing animation
            setInterval(() => {
                if (goingUp) {
                    yOffset -= 1;
                    if (yOffset <= -10) goingUp = false;
                } else {
                    yOffset += 1;
                    if (yOffset >= 10) goingUp = true;
                }

                sprite.position[1] += yOffset - sprite.position[1] % 1;
            }, 100);
        }

        // Save game state to local storage
        function saveGameState() {
            try {
                localStorage.setItem('tstoGameState', JSON.stringify(gameState));
                gameState.lastSave = Date.now();
                console.log('Game state saved');
            } catch (e) {
                console.error('Error saving game state:', e);
            }
        }

        // Initialize the game when the page loads
        window.addEventListener('load', initGame);
    </script>
</body>
</html>
