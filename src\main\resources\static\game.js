let currentUser = null;
let gameState = null;

// DOM Elements
const loginContainer = document.getElementById('login-container');
const gameContainer = document.getElementById('game-container');
const loginForm = document.getElementById('login-form');
const registerForm = document.getElementById('register-form');
const usernameInput = document.getElementById('username');
const passwordInput = document.getElementById('password');
const playerName = document.getElementById('player-name');
const playerLevel = document.getElementById('player-level');
const playerMoney = document.getElementById('player-money');
const playerDonuts = document.getElementById('player-donuts');
const logoutBtn = document.getElementById('logout-btn');
const townGrid = document.getElementById('town-grid');
const characterList = document.getElementById('character-list');
const shopItems = document.getElementById('shop-items');
const questList = document.getElementById('quest-list');
const friendList = document.getElementById('friend-list');
const friendRequestInput = document.getElementById('friend-request');
const sendRequestBtn = document.getElementById('send-request');

// Event Listeners
loginForm.addEventListener('submit', handleLogin);
registerForm.addEventListener('submit', handleRegister);
logoutBtn.addEventListener('click', handleLogout);
sendRequestBtn.addEventListener('click', handleFriendRequest);

// API Functions
async function handleLogin(e) {
    e.preventDefault();
    const username = usernameInput.value;
    const password = passwordInput.value;

    try {
        const response = await fetch('/api/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ username, password })
        });

        if (response.ok) {
            const data = await response.json();
            currentUser = data;
            updateUI();
            loginContainer.style.display = 'none';
            gameContainer.style.display = 'block';
            loadGameState();
        } else {
            alert('Login failed. Please check your credentials.');
        }
    } catch (error) {
        console.error('Login error:', error);
        alert('An error occurred during login.');
    }
}

async function handleRegister(e) {
    e.preventDefault();
    const username = usernameInput.value;
    const password = passwordInput.value;

    try {
        const response = await fetch('/api/register', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ username, password })
        });

        if (response.ok) {
            alert('Registration successful! Please login.');
        } else {
            alert('Registration failed. Username might be taken.');
        }
    } catch (error) {
        console.error('Registration error:', error);
        alert('An error occurred during registration.');
    }
}

async function handleLogout() {
    try {
        await fetch('/api/logout', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${currentUser.token}`
            }
        });
        currentUser = null;
        gameState = null;
        loginContainer.style.display = 'block';
        gameContainer.style.display = 'none';
        clearForms();
    } catch (error) {
        console.error('Logout error:', error);
    }
}

async function handleFriendRequest() {
    const friendUsername = friendRequestInput.value;
    if (!friendUsername) return;

    try {
        const response = await fetch('/api/friends/request', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${currentUser.token}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ username: friendUsername })
        });

        if (response.ok) {
            alert('Friend request sent!');
            friendRequestInput.value = '';
            loadFriends();
        } else {
            alert('Failed to send friend request.');
        }
    } catch (error) {
        console.error('Friend request error:', error);
        alert('An error occurred while sending friend request.');
    }
}

// Game State Functions
async function loadGameState() {
    try {
        const response = await fetch('/api/game/state', {
            headers: {
                'Authorization': `Bearer ${currentUser.token}`
            }
        });

        if (response.ok) {
            gameState = await response.json();
            updateGameUI();
        }
    } catch (error) {
        console.error('Error loading game state:', error);
    }
}

function updateGameUI() {
    if (!gameState) return;

    // Update town grid
    townGrid.innerHTML = '';
    gameState.town.forEach((tile, index) => {
        const tileElement = document.createElement('div');
        tileElement.className = 'tile';
        tileElement.textContent = tile.type;
        townGrid.appendChild(tileElement);
    });

    // Update characters
    characterList.innerHTML = '';
    gameState.characters.forEach(character => {
        const characterElement = document.createElement('div');
        characterElement.className = 'character';
        characterElement.textContent = `${character.name} (${character.isBusy ? 'Busy' : 'Available'})`;
        characterList.appendChild(characterElement);
    });

    // Update shop items
    shopItems.innerHTML = '';
    gameState.shopItems.forEach(item => {
        const itemElement = document.createElement('div');
        itemElement.className = 'shop-item';
        itemElement.textContent = `${item.name} - $${item.price}`;
        shopItems.appendChild(itemElement);
    });

    // Update quests
    questList.innerHTML = '';
    gameState.quests.forEach(quest => {
        const questElement = document.createElement('div');
        questElement.className = 'quest';
        questElement.textContent = `${quest.name} (${quest.isCompleted ? 'Completed' : 'In Progress'})`;
        questList.appendChild(questElement);
    });

    // Update friends
    loadFriends();
}

async function loadFriends() {
    try {
        const response = await fetch('/api/friends', {
            headers: {
                'Authorization': `Bearer ${currentUser.token}`
            }
        });

        if (response.ok) {
            const friends = await response.json();
            friendList.innerHTML = '';
            friends.forEach(friend => {
                const friendElement = document.createElement('div');
                friendElement.className = 'friend';
                friendElement.textContent = friend.username;
                friendList.appendChild(friendElement);
            });
        }
    } catch (error) {
        console.error('Error loading friends:', error);
    }
}

// UI Update Functions
function updateUI() {
    if (!currentUser) return;
    playerName.textContent = currentUser.username;
    playerLevel.textContent = `Level ${currentUser.level}`;
    playerMoney.textContent = `$${currentUser.money}`;
    playerDonuts.textContent = currentUser.donuts;
}

function clearForms() {
    usernameInput.value = '';
    passwordInput.value = '';
    friendRequestInput.value = '';
}

// Initialize
if (currentUser) {
    loginContainer.style.display = 'none';
    gameContainer.style.display = 'block';
    loadGameState();
} else {
    loginContainer.style.display = 'block';
    gameContainer.style.display = 'none';
} 