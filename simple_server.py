import http.server
import socketserver
import os
import json
from urllib.parse import urlparse, parse_qs

# Change directory to the static files directory
static_dir = os.path.join(os.getcwd(), "src", "main", "resources", "static")
if os.path.exists(static_dir):
    print(f"Serving files from: {static_dir}")
    os.chdir(static_dir)
else:
    print(f"Static directory not found at: {static_dir}")
    # Try to find the static directory
    for root, dirs, _ in os.walk(os.getcwd()):
        if "static" in dirs:
            static_dir = os.path.join(root, "static")
            print(f"Found static directory at: {static_dir}")
            if os.path.exists(static_dir):
                print(f"Serving files from: {static_dir}")
                os.chdir(static_dir)
                break

# List files in the current directory
print("Files in the current directory:")
for file in os.listdir():
    print(f"  - {file}")

class TSTOHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        print(f"GET request: {self.path}")

        # Handle API endpoints
        if self.path.startswith('/api/'):
            self.handle_api_get()
            return

        # Default to serving static files
        return super().do_GET()

    def do_POST(self):
        print(f"POST request: {self.path}")

        # Handle API endpoints
        if self.path.startswith('/api/'):
            self.handle_api_post()
            return

        # Default response for non-API POST requests
        self.send_error(404, "Not Found")

    def do_OPTIONS(self):
        print(f"OPTIONS request: {self.path}")
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()

    def handle_api_get(self):
        parts = self.path.split('/')
        endpoint = parts[2] if len(parts) > 2 else None

        if endpoint == 'gameState':
            # Return game state for the user
            query = urlparse(self.path).query
            params = parse_qs(query)
            user_id = params.get('userId', [''])[0]
            print(f"Getting game state for user: {user_id}")
            self.send_json_response({
                'success': True,
                'gameState': {
                    'money': 1000,
                    'donuts': 10,
                    'level': 1,
                    'experience': 0,
                    'buildings': [],
                    'characters': [
                        {'name': 'Homer', 'status': 'idle'},
                        {'name': 'Marge', 'status': 'idle'}
                    ]
                }
            })
        else:
            self.send_error(404, f"API endpoint not found: {endpoint}")

    def handle_api_post(self):
        content_length = int(self.headers.get('Content-Length', 0))
        if content_length > 0:
            post_data = self.rfile.read(content_length)
            try:
                data = json.loads(post_data.decode('utf-8'))
                print(f"POST data: {data}")
            except json.JSONDecodeError:
                print("Failed to parse JSON data")
                self.send_error(400, "Invalid JSON")
                return
        else:
            data = {}

        parts = self.path.split('/')
        endpoint = parts[2] if len(parts) > 2 else None

        if endpoint == 'login':
            self.handle_login(data)
        elif endpoint == 'register':
            self.handle_register(data)
        else:
            self.send_error(404, f"API endpoint not found: {endpoint}")

    def handle_login(self, data):
        username = data.get('username')
        password = data.get('password')  # Not used but kept for clarity

        print(f"Login attempt: {username}")

        # For testing, accept any login
        response = {
            'success': True,
            'userId': 'user123',
            'username': username,
            'level': 1,
            'experience': 0,
            'money': 1000,
            'donuts': 10,
            'token': 'dummy_token',
            'message': 'Login successful'
        }

        print(f"Login response: {response}")
        self.send_json_response(response)

    def handle_register(self, data):
        username = data.get('username')
        password = data.get('password')  # Not used but kept for clarity

        print(f"Registration attempt: {username}")

        # For testing, accept any registration
        response = {
            'success': True,
            'userId': 'user123',
            'username': username,
            'level': 1,
            'experience': 0,
            'money': 1000,
            'donuts': 10,
            'message': 'Registration successful'
        }

        print(f"Registration response: {response}")
        self.send_json_response(response)

    def send_json_response(self, data):
        print(f"Sending JSON response: {data}")
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
        response_json = json.dumps(data)
        print(f"Response JSON: {response_json}")
        self.wfile.write(response_json.encode())

# Set up the server
PORT = 8082  # Changed to 8082 to avoid conflicts
Handler = TSTOHandler

with socketserver.TCPServer(("", PORT), Handler) as httpd:
    print(f"Server running at http://localhost:{PORT}")
    httpd.serve_forever()
