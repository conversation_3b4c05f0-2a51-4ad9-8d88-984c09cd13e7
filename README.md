# TSTO Private Server (Java Version)

This is a Java implementation of The Simpsons: Tapped Out private server. The server provides basic functionality for handling game requests and managing player data.

## Prerequisites

- Java 11 or higher
- Maven 3.6 or higher

## Building the Project

To build the project, run:

```bash
mvn clean package
```

This will create two JAR files in the `target` directory:
- `tsto-server-1.0-SNAPSHOT.jar`: The main JAR file
- `tsto-server-1.0-SNAPSHOT-jar-with-dependencies.jar`: A fat JAR containing all dependencies

## Running the Server

You can run the server using the following command:

```bash
java -jar target/tsto-server-1.0-SNAPSHOT-jar-with-dependencies.jar [port]
```

Where `[port]` is an optional parameter to specify the port number (default is 8080).

## Features

The server currently implements the following endpoints:

- `/`: Root endpoint that returns a simple server status
- `/direction`: Direction endpoint (TODO)
- `/lobbyTime`: Lobby time endpoint (TODO)
- `/friendData`: Friend data endpoint (TODO)
- `/pluginEvent`: Plugin event endpoint (TODO)

## Project Structure

- `Main.java`: Entry point of the application
- `TSTOServer.java`: Main server implementation with HTTP handlers
- `pom.xml`: Maven build configuration

## Contributing

Feel free to contribute to this project by:
1. Forking the repository
2. Creating a feature branch
3. Making your changes
4. Submitting a pull request

## License

This project is licensed under the same terms as the original C++ implementation.

## Notes

This is a basic Java implementation of the TSTO private server. Some features from the original C++ implementation are still pending implementation:

- Protocol buffer message handling
- Friend data management
- Plugin event processing
- Security features
- Configuration management

Future updates will add these features progressively. 