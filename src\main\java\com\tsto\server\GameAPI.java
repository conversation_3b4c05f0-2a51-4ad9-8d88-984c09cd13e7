package com.tsto.server;

import com.sun.net.httpserver.HttpExchange;
import com.sun.net.httpserver.HttpHandler;

import java.io.IOException;
import java.io.OutputStream;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.List;
import java.util.ArrayList;

import org.json.JSONArray;
import org.json.JSONObject;
import java.util.logging.Logger;
import java.util.logging.Level;
import java.util.UUID;

import com.google.gson.JsonObject;
import com.google.gson.JsonParser;

public class GameAPI implements HttpHandler {
    private static final Logger logger = Logger.getLogger(GameAPI.class.getName());
    private final GameManager gameManager;
    private final Map<String, HttpHandler> handlers;

    public GameAPI(GameManager gameManager) {
        this.gameManager = gameManager;
        this.handlers = new HashMap<>();
        initializeHandlers();
    }

    private void initializeHandlers() {
        // Authentication endpoints
        handlers.put("POST /login", this::handleLogin);
        handlers.put("POST /register", this::handleRegister);
        
        // Game state endpoints
        handlers.put("GET /game/state", this::handleGameState);
        
        // Shop endpoints
        handlers.put("GET /shop/items", this::handleShopItems);
        handlers.put("POST /shop/purchase/{itemId}", this::handlePurchaseItem);
        
        // Quest endpoints
        handlers.put("GET /quests", this::handleQuests);
        handlers.put("POST /quests/start/{questId}", this::handleStartQuest);
        
        // Character endpoints
        handlers.put("GET /characters", this::handleCharacters);
        handlers.put("POST /characters/assign/{characterId}", this::handleCharacterTask);
        
        // Friend system endpoints
        handlers.put("GET /friends", this::handleFriends);
        handlers.put("POST /friends/visit/{friendId}", this::handleFriendVisit);
        handlers.put("POST /friends/remove/{friendId}", this::handleRemoveFriend);
        handlers.put("POST /friends/accept/{userId}", this::handleAcceptFriendRequest);
        handlers.put("POST /friends/reject/{userId}", this::handleRejectFriendRequest);
    }

    @Override
    public void handle(HttpExchange exchange) throws IOException {
        String method = exchange.getRequestMethod();
        String path = exchange.getRequestURI().getPath();
        
        logger.info("Original request path: " + path);
        
        // Remove /api prefix if present
        if (path.startsWith("/api")) {
            path = path.substring(4);
            logger.info("Path after /api removal: " + path);
        }
        
        String key = method + " " + path;
        logger.info("Looking for handler with key: " + key);

        HttpHandler handler = handlers.get(key);
        if (handler != null) {
            handler.handle(exchange);
        } else {
            logger.warning("No handler found for: " + key);
            // Log all registered handlers for debugging
            handlers.keySet().forEach(k -> logger.info("Registered handler: " + k));
            sendResponse(exchange, 404, "{\"error\":\"Not Found\"}");
        }
    }

    private void handleGameState(HttpExchange exchange) throws IOException {
        try {
            String token = exchange.getRequestHeaders().getFirst("Authorization");
            logger.info("Received Authorization header: " + (token != null ? token : "null"));
            
            if (token == null || !token.startsWith("Bearer ")) {
                logger.warning("Token missing or invalid format");
                sendErrorResponse(exchange, 401, "Unauthorized - Invalid token format");
                return;
            }
            
            token = token.substring(7); // Remove "Bearer " prefix
            logger.info("Validating token: " + token);
            
            if (!gameManager.validateToken(token)) {
                logger.warning("Token validation failed");
                sendErrorResponse(exchange, 401, "Unauthorized - Invalid token");
                return;
            }

            String username = gameManager.getUsernameFromToken(token);
            logger.info("Retrieved username from token: " + (username != null ? username : "null"));
            
            if (username == null) {
                logger.warning("No username found for token");
                sendErrorResponse(exchange, 401, "Invalid token");
                return;
            }

            User user = gameManager.getUser(username);
            if (user == null) {
                logger.warning("No user found for username: " + username);
                sendErrorResponse(exchange, 404, "User not found");
                return;
            }

            // Get game state
            GameState gameState = GameState.getInstance();
            JSONObject response = new JSONObject();
            response.put("success", true);
            response.put("user", createUserJson(user));
            response.put("buildings", createBuildingsJson(gameState.getBuildingsList()));
            response.put("characters", createCharactersJson(gameState.getCharactersList()));
            response.put("quests", createQuestsJson(gameState.getQuests().values()));
            response.put("shopItems", createShopItemsJson(gameState.getShopItems().values()));
            
            sendJsonResponse(exchange, response);
            logger.info("Game state loaded successfully for user: " + username);
        } catch (Exception e) {
            handleError("Error loading game state", e);
            sendErrorResponse(exchange, 500, "Internal server error");
        }
    }

    private void handleShopItems(HttpExchange exchange) throws IOException {
        JSONObject response = new JSONObject();
        response.put("success", true);
        response.put("items", createShopItemsJson(gameManager.getShopItems().values()));
        sendJsonResponse(exchange, response);
    }

    private void handleQuests(HttpExchange exchange) throws IOException {
        String userId = getUserIdFromRequest(exchange);
        User user = gameManager.getUser(userId);
        
        JSONObject response = new JSONObject();
        if (user != null) {
            response.put("success", true);
            response.put("quests", createQuestObjectsJson(gameManager.getQuests().values()));
        } else {
            response.put("success", false);
            response.put("message", "User not found");
        }
        
        sendJsonResponse(exchange, response);
    }

    private void handleCharacters(HttpExchange exchange) throws IOException {
        String userId = getUserIdFromRequest(exchange);
        User user = gameManager.getUser(userId);
        
        JSONObject response = new JSONObject();
        if (user != null) {
            response.put("success", true);
            response.put("characters", createCharactersJson(gameManager.getCharacters().values()));
        } else {
            response.put("success", false);
            response.put("message", "User not found");
        }
        
        sendJsonResponse(exchange, response);
    }

    private void handleCharacterTask(HttpExchange exchange) throws IOException {
        String requestBody = new String(exchange.getRequestBody().readAllBytes());
        JSONObject json = new JSONObject(requestBody);
        String characterId = json.getString("characterId");
        String taskId = json.getString("taskId");
        long duration = json.getLong("duration");

        Character character = gameManager.getCharacter(characterId);
        JSONObject response = new JSONObject();

        if (character != null) {
            boolean success = gameManager.assignTask(character, taskId, duration);
            response.put("success", success);
            if (success) {
                response.put("message", "Task assigned successfully");
                response.put("taskEndTime", character.getTaskEndTime());
            } else {
                response.put("message", "Failed to assign task");
            }
        } else {
            response.put("success", false);
            response.put("message", "Character not found");
        }

        sendJsonResponse(exchange, response);
    }

    private void handleFriends(HttpExchange exchange) throws IOException {
        String userId = getUserIdFromRequest(exchange);
        User user = gameManager.getUser(userId);
        
        JSONObject response = new JSONObject();
        if (user != null) {
            response.put("success", true);
            response.put("friends", new JSONArray(gameManager.getFriends(user.getUsername())));
            response.put("requests", new JSONArray(gameManager.getFriendRequests(user.getUsername())));
        } else {
            response.put("success", false);
            response.put("message", "User not found");
        }
        
        sendJsonResponse(exchange, response);
    }

    private void handleFriendVisit(HttpExchange exchange) throws IOException {
        String requestBody = new String(exchange.getRequestBody().readAllBytes());
        JSONObject json = new JSONObject(requestBody);
        String userId = json.getString("userId");
        String friendId = json.getString("friendId");

        User user = gameManager.getUser(userId);
        User friend = gameManager.getUser(friendId);
        
        JSONObject response = new JSONObject();
        if (user != null && friend != null) {
            boolean success = gameManager.visitFriendTown(user.getUsername(), friend.getUsername());
            response.put("success", success);
            if (success) {
                response.put("message", "Successfully visited friend's town");
            } else {
                response.put("message", "Failed to visit friend's town");
            }
        } else {
            response.put("success", false);
            response.put("message", "User or friend not found");
        }
        
        sendJsonResponse(exchange, response);
    }

    private boolean validateRequest(HttpExchange exchange) {
        String token = exchange.getRequestHeaders().getFirst("Authorization");
        return gameManager.validateToken(token);
    }

    private String getUserIdFromRequest(HttpExchange exchange) {
        return exchange.getRequestHeaders().getFirst("User-Id");
    }

    private void sendJsonResponse(HttpExchange exchange, JSONObject response) throws IOException {
        byte[] responseBytes = response.toString().getBytes();
        exchange.getResponseHeaders().set("Content-Type", "application/json");
        exchange.sendResponseHeaders(200, responseBytes.length);
        try (OutputStream os = exchange.getResponseBody()) {
            os.write(responseBytes);
        }
    }

    private void sendErrorResponse(HttpExchange exchange, int code, String message) throws IOException {
        JSONObject response = new JSONObject();
        response.put("success", false);
        response.put("message", message);
        byte[] responseBytes = response.toString().getBytes();
        exchange.getResponseHeaders().set("Content-Type", "application/json");
        exchange.sendResponseHeaders(code, responseBytes.length);
        try (OutputStream os = exchange.getResponseBody()) {
            os.write(responseBytes);
        }
    }

    private void handlePurchaseItem(HttpExchange exchange) throws IOException {
        String userId = getUserIdFromToken(exchange);
        if (userId == null) {
            sendResponse(exchange, 401, "{\"error\":\"Unauthorized\"}");
            return;
        }

        String itemId = getPathParameter(exchange, "itemId");
        if (itemId == null) {
            sendResponse(exchange, 400, "{\"error\":\"Invalid item ID\"}");
            return;
        }

        try {
            gameManager.purchaseItem(userId, itemId);
            sendResponse(exchange, 200, "{\"message\":\"Item purchased successfully\"}");
        } catch (Exception e) {
            sendResponse(exchange, 400, "{\"error\":\"" + e.getMessage() + "\"}");
        }
    }

    private void handleStartQuest(HttpExchange exchange) throws IOException {
        String userId = getUserIdFromToken(exchange);
        if (userId == null) {
            sendResponse(exchange, 401, "{\"error\":\"Unauthorized\"}");
            return;
        }

        String questId = getPathParameter(exchange, "questId");
        if (questId == null) {
            sendResponse(exchange, 400, "{\"error\":\"Invalid quest ID\"}");
            return;
        }

        try {
            gameManager.startQuest(userId, questId);
            sendResponse(exchange, 200, "{\"message\":\"Quest started successfully\"}");
        } catch (Exception e) {
            sendResponse(exchange, 400, "{\"error\":\"" + e.getMessage() + "\"}");
        }
    }

    private void handleRemoveFriend(HttpExchange exchange) throws IOException {
        String userId = getUserIdFromToken(exchange);
        if (userId == null) {
            sendResponse(exchange, 401, "{\"error\":\"Unauthorized\"}");
            return;
        }

        String friendId = getPathParameter(exchange, "friendId");
        if (friendId == null) {
            sendResponse(exchange, 400, "{\"error\":\"Invalid friend ID\"}");
            return;
        }

        try {
            gameManager.removeFriend(userId, friendId);
            sendResponse(exchange, 200, "{\"message\":\"Friend removed successfully\"}");
        } catch (Exception e) {
            sendResponse(exchange, 400, "{\"error\":\"" + e.getMessage() + "\"}");
        }
    }

    private void handleAcceptFriendRequest(HttpExchange exchange) throws IOException {
        String userId = getUserIdFromToken(exchange);
        if (userId == null) {
            sendResponse(exchange, 401, "{\"error\":\"Unauthorized\"}");
            return;
        }

        String requestUserId = getPathParameter(exchange, "userId");
        if (requestUserId == null) {
            sendResponse(exchange, 400, "{\"error\":\"Invalid user ID\"}");
            return;
        }

        try {
            gameManager.acceptFriendRequest(userId, requestUserId);
            sendResponse(exchange, 200, "{\"message\":\"Friend request accepted\"}");
        } catch (Exception e) {
            sendResponse(exchange, 400, "{\"error\":\"" + e.getMessage() + "\"}");
        }
    }

    private void handleRejectFriendRequest(HttpExchange exchange) throws IOException {
        String userId = getUserIdFromToken(exchange);
        if (userId == null) {
            sendResponse(exchange, 401, "{\"error\":\"Unauthorized\"}");
            return;
        }

        String requestUserId = getPathParameter(exchange, "userId");
        if (requestUserId == null) {
            sendResponse(exchange, 400, "{\"error\":\"Invalid user ID\"}");
            return;
        }

        try {
            gameManager.rejectFriendRequest(userId, requestUserId);
            sendResponse(exchange, 200, "{\"message\":\"Friend request rejected\"}");
        } catch (Exception e) {
            sendResponse(exchange, 400, "{\"error\":\"" + e.getMessage() + "\"}");
        }
    }

    private String getUserIdFromToken(HttpExchange exchange) {
        String authHeader = exchange.getRequestHeaders().getFirst("Authorization");
        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            return null;
        }
        String token = authHeader.substring(7);
        return gameManager.validateToken(token) ? token : null;
    }

    private String getPathParameter(HttpExchange exchange, String paramName) {
        String path = exchange.getRequestURI().getPath();
        String[] parts = path.split("/");
        for (int i = 0; i < parts.length - 1; i++) {
            if (parts[i].equals(paramName)) {
                return parts[i + 1];
            }
        }
        return null;
    }

    private void sendResponse(HttpExchange exchange, int statusCode, String response) throws IOException {
        exchange.getResponseHeaders().set("Content-Type", "application/json");
        exchange.sendResponseHeaders(statusCode, response.getBytes().length);
        OutputStream os = exchange.getResponseBody();
        os.write(response.getBytes());
        os.close();
    }

    private JSONObject createUserJson(User user) {
        JSONObject json = new JSONObject();
        json.put("userId", user.getUserId());
        json.put("username", user.getUsername());
        json.put("level", user.getLevel());
        json.put("experience", user.getExperience());
        json.put("money", user.getMoney());
        json.put("donuts", user.getDonuts());
        return json;
    }

    private JSONArray createShopItemsJson(Collection<ShopItem> items) {
        JSONArray jsonArray = new JSONArray();
        for (ShopItem item : items) {
            JSONObject json = new JSONObject();
            json.put("id", item.getId());
            json.put("name", item.getName());
            json.put("type", item.getType());
            json.put("price", item.getPrice());
            json.put("description", item.getDescription());
            json.put("imageUrl", item.getImageUrl());
            json.put("isPremium", item.isPremium());
            json.put("unlockLevel", item.getUnlockLevel());
            jsonArray.put(json);
        }
        return jsonArray;
    }

    private JSONArray createQuestsJson(Collection<Quest> quests) {
        JSONArray jsonArray = new JSONArray();
        for (Quest quest : quests) {
            JSONObject json = new JSONObject();
            json.put("id", quest.getId());
            json.put("name", quest.getName());
            json.put("type", quest.getType());
            json.put("description", quest.getDescription());
            json.put("isActive", quest.isActive());
            json.put("isCompleted", quest.isCompleted());
            
            // Add objectives
            JSONArray objectivesArray = new JSONArray();
            for (Quest.QuestObjective objective : quest.getObjectives()) {
                JSONObject objectiveJson = new JSONObject();
                objectiveJson.put("description", objective.getDescription());
                objectiveJson.put("targetCount", objective.getTargetCount());
                objectiveJson.put("currentCount", objective.getCurrentCount());
                objectivesArray.put(objectiveJson);
            }
            json.put("objectives", objectivesArray);
            
            // Add rewards
            json.put("rewards", new JSONObject(quest.getRewards()));
            
            jsonArray.put(json);
        }
        return jsonArray;
    }

    private JSONArray createQuestObjectsJson(Collection<QuestObject> quests) {
        JSONArray jsonArray = new JSONArray();
        for (QuestObject quest : quests) {
            JSONObject json = new JSONObject();
            json.put("id", quest.getId());
            json.put("name", quest.getName());
            json.put("type", quest.getType());
            json.put("isAvailable", quest.isAvailable());
            json.put("isCompleted", quest.isCompleted());
            
            // Add rewards
            JSONObject rewards = new JSONObject();
            rewards.put("experience", quest.getRewardExperience());
            rewards.put("money", quest.getRewardMoney());
            json.put("rewards", rewards);
            
            jsonArray.put(json);
        }
        return jsonArray;
    }

    private JSONArray createCharactersJson(Collection<Character> characters) {
        JSONArray jsonArray = new JSONArray();
        for (Character character : characters) {
            JSONObject json = new JSONObject();
            json.put("id", character.getId());
            json.put("name", character.getName());
            json.put("isBusy", character.isBusy());
            json.put("currentTask", character.getCurrentTask());
            json.put("taskEndTime", character.getTaskEndTime());
            jsonArray.put(json);
        }
        return jsonArray;
    }

    private JSONArray createBuildingsJson(List<Building> buildings) {
        JSONArray jsonArray = new JSONArray();
        for (Building building : buildings) {
            JSONObject json = new JSONObject();
            json.put("id", building.getId());
            json.put("name", building.getName());
            json.put("level", building.getLevel());
            jsonArray.put(json);
        }
        return jsonArray;
    }

    private void logInfo(String message, Object... params) {
        logger.log(Level.INFO, message, params);
    }

    private void logWarning(String message, Object... params) {
        logger.log(Level.WARNING, message, params);
    }

    private void handleError(String message, Exception e) {
        logger.log(Level.SEVERE, message, e);
    }

    private void handleLogin(HttpExchange exchange) throws IOException {
        try {
            // Read request body
            String requestBody = new String(exchange.getRequestBody().readAllBytes());
            JsonObject json = JsonParser.parseString(requestBody).getAsJsonObject();
            
            // Extract credentials
            String username = json.get("username").getAsString();
            String password = json.get("password").getAsString();
            
            if (username == null || password == null || username.isEmpty() || password.isEmpty()) {
                sendErrorResponse(exchange, 400, "Username and password are required");
                return;
            }
            
            // Authenticate user
            User user = gameManager.authenticateUser(username, password);
            if (user != null) {
                // Generate session token
                String token = UUID.randomUUID().toString();
                gameManager.registerUser(username, token);
                
                // Create response
                JSONObject response = new JSONObject();
                response.put("success", true);
                response.put("token", token);
                response.put("user", createUserJson(user));
                
                // Set response headers
                exchange.getResponseHeaders().set("Content-Type", "application/json");
                exchange.getResponseHeaders().set("Authorization", token);
                
                sendJsonResponse(exchange, response);
                logger.info("User logged in successfully: " + username);
            } else {
                sendErrorResponse(exchange, 401, "Invalid username or password");
                logger.warning("Failed login attempt for user: " + username);
            }
        } catch (Exception e) {
            handleError("Error during login", e);
            sendErrorResponse(exchange, 500, "Internal server error");
        }
    }

    private void handleRegister(HttpExchange exchange) throws IOException {
        try {
            // Read request body
            String requestBody = new String(exchange.getRequestBody().readAllBytes());
            JsonObject json = JsonParser.parseString(requestBody).getAsJsonObject();
            
            // Extract registration data
            String username = json.get("username").getAsString();
            String password = json.get("password").getAsString();
            
            if (username == null || password == null || username.isEmpty() || password.isEmpty()) {
                sendErrorResponse(exchange, 400, "Username and password are required");
                return;
            }
            
            // Validate username length
            if (username.length() < 3 || username.length() > 20) {
                sendErrorResponse(exchange, 400, "Username must be between 3 and 20 characters");
                return;
            }
            
            // Validate password length
            if (password.length() < 6) {
                sendErrorResponse(exchange, 400, "Password must be at least 6 characters");
                return;
            }
            
            // Create user
            User user = gameManager.createUser(username, password);
            if (user != null) {
                // Generate session token
                String token = UUID.randomUUID().toString();
                gameManager.registerUser(username, token);
                
                // Create response
                JSONObject response = new JSONObject();
                response.put("success", true);
                response.put("token", token);
                response.put("user", createUserJson(user));
                
                // Set response headers
                exchange.getResponseHeaders().set("Content-Type", "application/json");
                exchange.getResponseHeaders().set("Authorization", token);
                
                sendJsonResponse(exchange, response);
                logger.info("New user registered successfully: " + username);
            } else {
                sendErrorResponse(exchange, 409, "Username already exists");
                logger.warning("Failed registration attempt for username: " + username);
            }
        } catch (Exception e) {
            handleError("Error during registration", e);
            sendErrorResponse(exchange, 500, "Internal server error");
        }
    }

    private String hashPassword(String password) {
        try {
            java.security.MessageDigest md = java.security.MessageDigest.getInstance("SHA-256");
            byte[] hash = md.digest(password.getBytes());
            return java.util.Base64.getEncoder().encodeToString(hash);
        } catch (java.security.NoSuchAlgorithmException e) {
            logger.log(Level.SEVERE, "Error hashing password", e);
            return null;
        }
    }
} 