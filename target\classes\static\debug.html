<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TSTO Debug Dashboard</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        h1, h2, h3 {
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .card {
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid #ddd;
        }
        
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            border: 1px solid transparent;
            border-bottom: none;
            border-radius: 5px 5px 0 0;
            margin-right: 5px;
        }
        
        .tab.active {
            background-color: #fff;
            border-color: #ddd;
            border-bottom-color: #fff;
            margin-bottom: -1px;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        input, button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
        }
        
        button:hover {
            background-color: #45a049;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        th {
            background-color: #f2f2f2;
        }
        
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
        
        .status {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status-success {
            background-color: #dff0d8;
            color: #3c763d;
        }
        
        .status-error {
            background-color: #f2dede;
            color: #a94442;
        }
        
        .status-info {
            background-color: #d9edf7;
            color: #31708f;
        }
        
        .refresh-btn {
            background-color: #2196F3;
            margin-left: 10px;
        }
        
        .clear-btn {
            background-color: #f44336;
        }
        
        .action-btn {
            margin-right: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>TSTO Debug Dashboard</h1>
        
        <div class="card">
            <h2>Server Status <button id="refreshStatus" class="refresh-btn">Refresh</button></h2>
            <div id="serverStatus">Loading...</div>
        </div>
        
        <div class="tabs">
            <div class="tab active" data-tab="requests">Requests</div>
            <div class="tab" data-tab="responses">Responses</div>
            <div class="tab" data-tab="errors">Errors</div>
            <div class="tab" data-tab="users">Users</div>
            <div class="tab" data-tab="sessions">Sessions</div>
            <div class="tab" data-tab="tools">Tools</div>
        </div>
        
        <div class="tab-content active" id="requests">
            <div class="card">
                <h2>Recent Requests <button id="refreshRequests" class="refresh-btn">Refresh</button> <button id="clearRequests" class="clear-btn">Clear</button></h2>
                <div id="requestsList">Loading...</div>
            </div>
        </div>
        
        <div class="tab-content" id="responses">
            <div class="card">
                <h2>Recent Responses <button id="refreshResponses" class="refresh-btn">Refresh</button> <button id="clearResponses" class="clear-btn">Clear</button></h2>
                <div id="responsesList">Loading...</div>
            </div>
        </div>
        
        <div class="tab-content" id="errors">
            <div class="card">
                <h2>Recent Errors <button id="refreshErrors" class="refresh-btn">Refresh</button> <button id="clearErrors" class="clear-btn">Clear</button></h2>
                <div id="errorsList">Loading...</div>
            </div>
        </div>
        
        <div class="tab-content" id="users">
            <div class="card">
                <h2>Registered Users <button id="refreshUsers" class="refresh-btn">Refresh</button></h2>
                <div id="usersList">Loading...</div>
            </div>
            
            <div class="card">
                <h2>Add Test User</h2>
                <form id="addUserForm">
                    <div class="form-group">
                        <label for="username">Username:</label>
                        <input type="text" id="username" required>
                    </div>
                    <div class="form-group">
                        <label for="password">Password:</label>
                        <input type="password" id="password" required>
                    </div>
                    <button type="submit">Add User</button>
                </form>
            </div>
        </div>
        
        <div class="tab-content" id="sessions">
            <div class="card">
                <h2>Active Sessions <button id="refreshSessions" class="refresh-btn">Refresh</button> <button id="clearSessions" class="clear-btn">Clear</button></h2>
                <div id="sessionsList">Loading...</div>
            </div>
        </div>
        
        <div class="tab-content" id="tools">
            <div class="card">
                <h2>Debug Tools</h2>
                <button id="resetStorage" class="clear-btn">Reset Debug Storage</button>
                <button id="testLogin" class="action-btn">Test Login</button>
                <button id="testRegister" class="action-btn">Test Register</button>
                <button id="testSave" class="action-btn">Test Save Game</button>
                <button id="testLoad" class="action-btn">Test Load Game</button>
            </div>
            
            <div class="card">
                <h2>API Tester</h2>
                <div class="form-group">
                    <label for="apiEndpoint">Endpoint:</label>
                    <select id="apiEndpoint">
                        <option value="/api/login">Login</option>
                        <option value="/api/register">Register</option>
                        <option value="/api/save">Save Game</option>
                        <option value="/api/load">Load Game</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="apiPayload">Payload (JSON):</label>
                    <textarea id="apiPayload" rows="5" style="width: 100%;">{
  "username": "testuser",
  "password": "testpassword"
}</textarea>
                </div>
                <button id="sendApiRequest">Send Request</button>
                <div class="form-group">
                    <label for="apiResponse">Response:</label>
                    <pre id="apiResponse"></pre>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Tab switching
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', () => {
                document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));
                
                tab.classList.add('active');
                document.getElementById(tab.dataset.tab).classList.add('active');
            });
        });
        
        // API functions
        async function fetchDebugData(endpoint) {
            try {
                const response = await fetch(`/debug/${endpoint}`);
                return await response.json();
            } catch (error) {
                console.error(`Error fetching ${endpoint}:`, error);
                return { error: `Failed to fetch ${endpoint}` };
            }
        }
        
        async function postDebugData(endpoint, data) {
            try {
                const response = await fetch(`/debug/${endpoint}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });
                return await response.json();
            } catch (error) {
                console.error(`Error posting to ${endpoint}:`, error);
                return { error: `Failed to post to ${endpoint}` };
            }
        }
        
        // Refresh functions
        async function refreshStatus() {
            const statusData = await fetchDebugData('status');
            const statusElement = document.getElementById('serverStatus');
            
            if (statusData.error) {
                statusElement.innerHTML = `<div class="status status-error">${statusData.error}</div>`;
                return;
            }
            
            const uptime = Math.floor(statusData.uptime);
            const hours = Math.floor(uptime / 3600);
            const minutes = Math.floor((uptime % 3600) / 60);
            const seconds = uptime % 60;
            
            statusElement.innerHTML = `
                <table>
                    <tr>
                        <th>Status</th>
                        <td><div class="status status-success">${statusData.status}</div></td>
                    </tr>
                    <tr>
                        <th>Uptime</th>
                        <td>${hours}h ${minutes}m ${seconds}s</td>
                    </tr>
                    <tr>
                        <th>Request Count</th>
                        <td>${statusData.request_count}</td>
                    </tr>
                    <tr>
                        <th>Error Count</th>
                        <td>${statusData.error_count}</td>
                    </tr>
                    <tr>
                        <th>User Count</th>
                        <td>${statusData.user_count}</td>
                    </tr>
                    <tr>
                        <th>Session Count</th>
                        <td>${statusData.session_count}</td>
                    </tr>
                </table>
            `;
        }
        
        async function refreshRequests() {
            const requestsData = await fetchDebugData('requests');
            const requestsElement = document.getElementById('requestsList');
            
            if (requestsData.error) {
                requestsElement.innerHTML = `<div class="status status-error">${requestsData.error}</div>`;
                return;
            }
            
            if (!requestsData.requests || requestsData.requests.length === 0) {
                requestsElement.innerHTML = '<p>No requests recorded yet.</p>';
                return;
            }
            
            const requests = requestsData.requests.reverse().slice(0, 10);
            
            requestsElement.innerHTML = `
                <table>
                    <tr>
                        <th>ID</th>
                        <th>Method</th>
                        <th>Path</th>
                        <th>Client</th>
                        <th>Timestamp</th>
                    </tr>
                    ${requests.map(req => `
                        <tr>
                            <td>${req.id}</td>
                            <td>${req.method}</td>
                            <td>${req.path}</td>
                            <td>${req.client_address}</td>
                            <td>${new Date(req.timestamp * 1000).toLocaleString()}</td>
                        </tr>
                    `).join('')}
                </table>
            `;
        }
        
        async function refreshResponses() {
            const responsesData = await fetchDebugData('responses');
            const responsesElement = document.getElementById('responsesList');
            
            if (responsesData.error) {
                responsesElement.innerHTML = `<div class="status status-error">${responsesData.error}</div>`;
                return;
            }
            
            if (!responsesData.responses || responsesData.responses.length === 0) {
                responsesElement.innerHTML = '<p>No responses recorded yet.</p>';
                return;
            }
            
            const responses = responsesData.responses.reverse().slice(0, 10);
            
            responsesElement.innerHTML = `
                <table>
                    <tr>
                        <th>Request ID</th>
                        <th>Status</th>
                        <th>Timestamp</th>
                        <th>Data</th>
                    </tr>
                    ${responses.map(res => `
                        <tr>
                            <td>${res.request_id}</td>
                            <td>${res.status_code}</td>
                            <td>${new Date(res.timestamp * 1000).toLocaleString()}</td>
                            <td><pre>${JSON.stringify(res.response_data, null, 2)}</pre></td>
                        </tr>
                    `).join('')}
                </table>
            `;
        }
        
        async function refreshErrors() {
            const errorsData = await fetchDebugData('errors');
            const errorsElement = document.getElementById('errorsList');
            
            if (errorsData.error) {
                errorsElement.innerHTML = `<div class="status status-error">${errorsData.error}</div>`;
                return;
            }
            
            if (!errorsData.errors || errorsData.errors.length === 0) {
                errorsElement.innerHTML = '<p>No errors recorded yet.</p>';
                return;
            }
            
            const errors = errorsData.errors.reverse().slice(0, 10);
            
            errorsElement.innerHTML = `
                <table>
                    <tr>
                        <th>Request ID</th>
                        <th>Error Type</th>
                        <th>Message</th>
                        <th>Timestamp</th>
                    </tr>
                    ${errors.map(err => `
                        <tr>
                            <td>${err.request_id}</td>
                            <td>${err.error_type}</td>
                            <td>${err.error_message}</td>
                            <td>${new Date(err.timestamp * 1000).toLocaleString()}</td>
                        </tr>
                    `).join('')}
                </table>
            `;
        }
        
        async function refreshUsers() {
            const usersData = await fetchDebugData('users');
            const usersElement = document.getElementById('usersList');
            
            if (usersData.error) {
                usersElement.innerHTML = `<div class="status status-error">${usersData.error}</div>`;
                return;
            }
            
            if (!usersData.users || Object.keys(usersData.users).length === 0) {
                usersElement.innerHTML = '<p>No users registered yet.</p>';
                return;
            }
            
            usersElement.innerHTML = `
                <table>
                    <tr>
                        <th>Username</th>
                        <th>User ID</th>
                        <th>Level</th>
                        <th>Money</th>
                        <th>Donuts</th>
                    </tr>
                    ${Object.entries(usersData.users).map(([username, userData]) => `
                        <tr>
                            <td>${username}</td>
                            <td>${userData.userId}</td>
                            <td>${userData.level}</td>
                            <td>${userData.money}</td>
                            <td>${userData.donuts}</td>
                        </tr>
                    `).join('')}
                </table>
            `;
        }
        
        async function refreshSessions() {
            const sessionsData = await fetchDebugData('sessions');
            const sessionsElement = document.getElementById('sessionsList');
            
            if (sessionsData.error) {
                sessionsElement.innerHTML = `<div class="status status-error">${sessionsData.error}</div>`;
                return;
            }
            
            if (!sessionsData.sessions || Object.keys(sessionsData.sessions).length === 0) {
                sessionsElement.innerHTML = '<p>No active sessions.</p>';
                return;
            }
            
            sessionsElement.innerHTML = `
                <table>
                    <tr>
                        <th>Token</th>
                        <th>Username</th>
                        <th>User ID</th>
                        <th>Created At</th>
                    </tr>
                    ${Object.entries(sessionsData.sessions).map(([token, sessionData]) => `
                        <tr>
                            <td>${token}</td>
                            <td>${sessionData.username}</td>
                            <td>${sessionData.userId}</td>
                            <td>${new Date(sessionData.created_at * 1000).toLocaleString()}</td>
                        </tr>
                    `).join('')}
                </table>
            `;
        }
        
        // Event listeners
        document.getElementById('refreshStatus').addEventListener('click', refreshStatus);
        document.getElementById('refreshRequests').addEventListener('click', refreshRequests);
        document.getElementById('refreshResponses').addEventListener('click', refreshResponses);
        document.getElementById('refreshErrors').addEventListener('click', refreshErrors);
        document.getElementById('refreshUsers').addEventListener('click', refreshUsers);
        document.getElementById('refreshSessions').addEventListener('click', refreshSessions);
        
        document.getElementById('clearRequests').addEventListener('click', async () => {
            await postDebugData('reset', { target: 'requests' });
            refreshRequests();
        });
        
        document.getElementById('clearResponses').addEventListener('click', async () => {
            await postDebugData('reset', { target: 'responses' });
            refreshResponses();
        });
        
        document.getElementById('clearErrors').addEventListener('click', async () => {
            await postDebugData('reset', { target: 'errors' });
            refreshErrors();
        });
        
        document.getElementById('clearSessions').addEventListener('click', async () => {
            await postDebugData('reset', { target: 'sessions' });
            refreshSessions();
        });
        
        document.getElementById('resetStorage').addEventListener('click', async () => {
            await postDebugData('reset', {});
            refreshStatus();
            refreshRequests();
            refreshResponses();
            refreshErrors();
            refreshUsers();
            refreshSessions();
        });
        
        document.getElementById('addUserForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            await postDebugData('add_user', { username, password });
            refreshUsers();
            
            document.getElementById('username').value = '';
            document.getElementById('password').value = '';
        });
        
        document.getElementById('testLogin').addEventListener('click', async () => {
            const response = await fetch('/api/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    username: 'testuser',
                    password: 'testpassword'
                })
            });
            
            const data = await response.json();
            alert(`Login test result: ${data.success ? 'Success' : 'Failed'}\n${data.message}`);
            
            refreshRequests();
            refreshResponses();
            refreshSessions();
        });
        
        document.getElementById('testRegister').addEventListener('click', async () => {
            const username = `test_${Date.now()}`;
            const response = await fetch('/api/register', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    username,
                    password: 'testpassword'
                })
            });
            
            const data = await response.json();
            alert(`Register test result: ${data.success ? 'Success' : 'Failed'}\n${data.message}`);
            
            refreshRequests();
            refreshResponses();
            refreshUsers();
        });
        
        document.getElementById('testSave').addEventListener('click', async () => {
            // First login to get a user ID
            const loginResponse = await fetch('/api/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    username: 'testuser',
                    password: 'testpassword'
                })
            });
            
            const loginData = await loginResponse.json();
            
            if (!loginData.success) {
                alert(`Login failed: ${loginData.message}`);
                return;
            }
            
            // Then save game
            const saveResponse = await fetch('/api/save', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    user_id: loginData.userId,
                    game_state: {
                        money: 2000,
                        donuts: 20,
                        level: 2,
                        experience: 50,
                        buildings: [
                            { name: 'House', level: 1, income: 10 }
                        ],
                        characters: [
                            { name: 'Homer', status: 'working' },
                            { name: 'Marge', status: 'idle' }
                        ]
                    }
                })
            });
            
            const saveData = await saveResponse.json();
            alert(`Save test result: ${saveData.success ? 'Success' : 'Failed'}\n${saveData.message}`);
            
            refreshRequests();
            refreshResponses();
            refreshUsers();
        });
        
        document.getElementById('testLoad').addEventListener('click', async () => {
            // First login to get a user ID
            const loginResponse = await fetch('/api/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    username: 'testuser',
                    password: 'testpassword'
                })
            });
            
            const loginData = await loginResponse.json();
            
            if (!loginData.success) {
                alert(`Login failed: ${loginData.message}`);
                return;
            }
            
            // Then load game
            const loadResponse = await fetch('/api/load', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    user_id: loginData.userId
                })
            });
            
            const loadData = await loadResponse.json();
            alert(`Load test result: ${loadData.success ? 'Success' : 'Failed'}\n${loadData.message}`);
            
            refreshRequests();
            refreshResponses();
        });
        
        document.getElementById('sendApiRequest').addEventListener('click', async () => {
            const endpoint = document.getElementById('apiEndpoint').value;
            const payload = document.getElementById('apiPayload').value;
            
            try {
                const payloadObj = JSON.parse(payload);
                
                const response = await fetch(endpoint, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(payloadObj)
                });
                
                const data = await response.json();
                document.getElementById('apiResponse').textContent = JSON.stringify(data, null, 2);
                
                refreshRequests();
                refreshResponses();
            } catch (error) {
                document.getElementById('apiResponse').textContent = `Error: ${error.message}`;
            }
        });
        
        // Initial load
        refreshStatus();
        refreshRequests();
        refreshResponses();
        refreshErrors();
        refreshUsers();
        refreshSessions();
        
        // Auto-refresh every 5 seconds
        setInterval(refreshStatus, 5000);
    </script>
</body>
</html>
