package com.tsto.server;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.logging.Logger;
import java.util.logging.Level;

public class TaskManager {
    private static final Logger LOGGER = Logger.getLogger(TaskManager.class.getName());
    private static TaskManager instance;
    private final GameState gameState;
    private final ScheduledExecutorService scheduler;
    private final ConcurrentHashMap<String, Task> activeTasks;

    private TaskManager() {
        this.gameState = GameState.getInstance();
        this.scheduler = Executors.newScheduledThreadPool(10);
        this.activeTasks = new ConcurrentHashMap<>();
    }

    public static synchronized TaskManager getInstance() {
        if (instance == null) {
            instance = new TaskManager();
        }
        return instance;
    }

    public boolean startTask(String characterId, String taskId) {
        Character character = gameState.getCharacter(characterId);
        if (character == null || character.isBusy()) {
            return false;
        }

        int duration = character.getTaskDuration(taskId);
        if (duration <= 0) {
            return false;
        }

        character.setBusy(true);
        character.setTaskEndTime(System.currentTimeMillis() + (duration * 1000L));

        Task task = new Task(characterId, taskId, duration);
        activeTasks.put(characterId, task);

        scheduler.schedule(() -> completeTask(characterId), duration, TimeUnit.SECONDS);
        LOGGER.info("Started task " + taskId + " for character " + characterId);
        return true;
    }

    private void completeTask(String characterId) {
        Task task = activeTasks.remove(characterId);
        if (task == null) {
            return;
        }

        Character character = gameState.getCharacter(characterId);
        if (character != null) {
            character.setBusy(false);
            // Add rewards
            gameState.addMoney(100); // Base reward
            gameState.addExperience(50); // Base experience
            LOGGER.info("Completed task " + task.getTaskId() + " for character " + characterId);
        }
    }

    public void cancelTask(String characterId) {
        Task task = activeTasks.remove(characterId);
        if (task != null) {
            Character character = gameState.getCharacter(characterId);
            if (character != null) {
                character.setBusy(false);
                LOGGER.info("Cancelled task for character " + characterId);
            }
        }
    }

    public boolean isCharacterBusy(String characterId) {
        Character character = gameState.getCharacter(characterId);
        return character != null && character.isBusy();
    }

    public long getTaskRemainingTime(String characterId) {
        Character character = gameState.getCharacter(characterId);
        if (character == null || !character.isBusy()) {
            return 0;
        }
        return Math.max(0, character.getTaskEndTime() - System.currentTimeMillis());
    }

    public void shutdown() {
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(60, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            LOGGER.log(Level.SEVERE, "Error shutting down task scheduler", e);
            scheduler.shutdownNow();
        }
    }

    private static class Task {
        private final String characterId;
        private final String taskId;
        private final int duration;

        public Task(String characterId, String taskId, int duration) {
            this.characterId = characterId;
            this.taskId = taskId;
            this.duration = duration;
        }

        public String getCharacterId() {
            return characterId;
        }

        public String getTaskId() {
            return taskId;
        }

        public int getDuration() {
            return duration;
        }
    }
} 