package com.tsto.server;

import java.io.Serializable;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.Map;

public class DailyReward implements Serializable {
    private static final long serialVersionUID = 1L;
    
    private int currentStreak;
    private long lastClaimTime;
    private Map<Integer, Reward> rewards;

    public static class Reward implements Serializable {
        private int money;
        private int donuts;
        private int experience;
        private Map<String, Integer> items;

        public Reward(int money, int donuts, int experience) {
            this.money = money;
            this.donuts = donuts;
            this.experience = experience;
            this.items = new HashMap<>();
        }

        public void addItem(String itemId, int quantity) {
            items.put(itemId, quantity);
        }

        public int getMoney() { return money; }
        public int getDonuts() { return donuts; }
        public int getExperience() { return experience; }
        public Map<String, Integer> getItems() { return new HashMap<>(items); }
    }

    public DailyReward() {
        this.currentStreak = 0;
        this.lastClaimTime = 0;
        this.rewards = new HashMap<>();
        initializeRewards();
    }

    private void initializeRewards() {
        // Day 1
        rewards.put(1, new Reward(1000, 1, 100));
        // Day 2
        rewards.put(2, new Reward(2000, 2, 200));
        // Day 3
        rewards.put(3, new Reward(3000, 3, 300));
        // Day 4
        rewards.put(4, new Reward(4000, 4, 400));
        // Day 5
        rewards.put(5, new Reward(5000, 5, 500));
        // Day 6
        rewards.put(6, new Reward(6000, 6, 600));
        // Day 7 (Weekly completion bonus)
        Reward weeklyReward = new Reward(10000, 10, 1000);
        weeklyReward.addItem("premium_mystery_box", 1);
        rewards.put(7, weeklyReward);
    }

    public boolean canClaim() {
        if (lastClaimTime == 0) return true;
        
        Instant lastClaim = Instant.ofEpochSecond(lastClaimTime);
        Instant now = Instant.now();
        
        // Check if it's been more than 24 hours since last claim
        long hoursSinceLastClaim = ChronoUnit.HOURS.between(lastClaim, now);
        
        // If more than 48 hours have passed, reset streak
        if (hoursSinceLastClaim >= 48) {
            currentStreak = 0;
            return true;
        }
        
        // Can claim if more than 20 hours have passed (giving 4-hour buffer)
        return hoursSinceLastClaim >= 20;
    }

    public Reward claim() {
        if (!canClaim()) {
            return null;
        }

        currentStreak = (currentStreak % 7) + 1;
        lastClaimTime = Instant.now().getEpochSecond();
        return rewards.get(currentStreak);
    }

    public int getCurrentStreak() {
        return currentStreak;
    }

    public long getLastClaimTime() {
        return lastClaimTime;
    }

    public Reward getNextReward() {
        int nextDay = (currentStreak % 7) + 1;
        return rewards.get(nextDay);
    }

    public long getTimeUntilNextClaim() {
        if (canClaim()) return 0;
        
        Instant lastClaim = Instant.ofEpochSecond(lastClaimTime);
        Instant now = Instant.now();
        long hoursSinceLastClaim = ChronoUnit.HOURS.between(lastClaim, now);
        
        return Math.max(0, 20 - hoursSinceLastClaim);
    }
} 