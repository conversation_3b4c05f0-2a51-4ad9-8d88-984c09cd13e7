/**
 * WebGL Renderer for TSTO Web Game
 * Provides hardware-accelerated rendering for improved performance
 */

class WebGLRenderer {
    /**
     * Initialize the WebGL renderer
     * @param {HTMLCanvasElement} canvas - The canvas element to render to
     */
    constructor(canvas) {
        this.canvas = canvas;
        this.gl = null;
        this.program = null;
        this.textures = {};
        this.textureAtlas = null;
        this.spriteBuffer = null;
        this.sprites = [];
        this.cameraX = 0;
        this.cameraY = 0;
        this.scale = 1;

        // Shader attributes and uniforms
        this.positionAttrib = null;
        this.texCoordAttrib = null;
        this.colorAttrib = null;
        this.matrixUniform = null;
        this.textureUniform = null;

        // Initialize WebGL
        this.initWebGL();
    }

    /**
     * Initialize WebGL context and set up shaders
     */
    initWebGL() {
        try {
            // Get WebGL context
            this.gl = this.canvas.getContext('webgl') || this.canvas.getContext('experimental-webgl');

            if (!this.gl) {
                console.error('WebGL not supported');
                return false;
            }

            // Set up viewport
            this.resizeCanvas();

            // Create shaders
            this.createShaders();

            // Create buffers
            this.createBuffers();

            // Enable alpha blending
            this.gl.enable(this.gl.BLEND);
            this.gl.blendFunc(this.gl.SRC_ALPHA, this.gl.ONE_MINUS_SRC_ALPHA);

            // Set clear color (bright green as fallback for grass)
            this.gl.clearColor(0.5, 0.8, 0.3, 1.0);

            console.log('WebGL initialized successfully');
            return true;
        } catch (e) {
            console.error('Error initializing WebGL:', e);
            return false;
        }
    }

    /**
     * Create and compile shaders
     */
    createShaders() {
        // Vertex shader source
        const vsSource = `
            attribute vec2 a_position;
            attribute vec2 a_texCoord;
            attribute vec4 a_color;

            uniform mat3 u_matrix;

            varying vec2 v_texCoord;
            varying vec4 v_color;

            void main() {
                vec3 position = u_matrix * vec3(a_position, 1.0);
                gl_Position = vec4(position.xy, 0.0, 1.0);
                v_texCoord = a_texCoord;
                v_color = a_color;
            }
        `;

        // Fragment shader source
        const fsSource = `
            precision mediump float;

            uniform sampler2D u_texture;

            varying vec2 v_texCoord;
            varying vec4 v_color;

            void main() {
                vec4 texColor = texture2D(u_texture, v_texCoord);
                gl_FragColor = texColor * v_color;
            }
        `;

        // Create and compile vertex shader
        const vertexShader = this.gl.createShader(this.gl.VERTEX_SHADER);
        this.gl.shaderSource(vertexShader, vsSource);
        this.gl.compileShader(vertexShader);

        // Check for compilation errors
        if (!this.gl.getShaderParameter(vertexShader, this.gl.COMPILE_STATUS)) {
            console.error('Vertex shader compilation failed:', this.gl.getShaderInfoLog(vertexShader));
            this.gl.deleteShader(vertexShader);
            return false;
        }

        // Create and compile fragment shader
        const fragmentShader = this.gl.createShader(this.gl.FRAGMENT_SHADER);
        this.gl.shaderSource(fragmentShader, fsSource);
        this.gl.compileShader(fragmentShader);

        // Check for compilation errors
        if (!this.gl.getShaderParameter(fragmentShader, this.gl.COMPILE_STATUS)) {
            console.error('Fragment shader compilation failed:', this.gl.getShaderInfoLog(fragmentShader));
            this.gl.deleteShader(vertexShader);
            this.gl.deleteShader(fragmentShader);
            return false;
        }

        // Create shader program
        this.program = this.gl.createProgram();
        this.gl.attachShader(this.program, vertexShader);
        this.gl.attachShader(this.program, fragmentShader);
        this.gl.linkProgram(this.program);

        // Check for linking errors
        if (!this.gl.getProgramParameter(this.program, this.gl.LINK_STATUS)) {
            console.error('Shader program linking failed:', this.gl.getProgramInfoLog(this.program));
            this.gl.deleteProgram(this.program);
            this.gl.deleteShader(vertexShader);
            this.gl.deleteShader(fragmentShader);
            return false;
        }

        // Get attribute and uniform locations
        this.positionAttrib = this.gl.getAttribLocation(this.program, 'a_position');
        this.texCoordAttrib = this.gl.getAttribLocation(this.program, 'a_texCoord');
        this.colorAttrib = this.gl.getAttribLocation(this.program, 'a_color');
        this.matrixUniform = this.gl.getUniformLocation(this.program, 'u_matrix');
        this.textureUniform = this.gl.getUniformLocation(this.program, 'u_texture');

        // Enable attributes
        this.gl.enableVertexAttribArray(this.positionAttrib);
        this.gl.enableVertexAttribArray(this.texCoordAttrib);
        this.gl.enableVertexAttribArray(this.colorAttrib);

        return true;
    }

    /**
     * Create vertex buffers
     */
    createBuffers() {
        // Create sprite buffer
        this.spriteBuffer = this.gl.createBuffer();
    }

    /**
     * Resize canvas to match display size
     */
    resizeCanvas() {
        // Get the display size of the canvas
        const displayWidth = this.canvas.clientWidth;
        const displayHeight = this.canvas.clientHeight;

        // Check if the canvas is not the same size
        if (this.canvas.width !== displayWidth || this.canvas.height !== displayHeight) {
            // Make the canvas the same size
            this.canvas.width = displayWidth;
            this.canvas.height = displayHeight;

            // Update the viewport
            this.gl.viewport(0, 0, this.canvas.width, this.canvas.height);
        }
    }

    /**
     * Load a texture from an image URL
     * @param {string} name - The name to reference the texture by
     * @param {string} url - The URL of the image
     * @returns {Promise} - A promise that resolves when the texture is loaded
     */
    loadTexture(name, url) {
        return new Promise((resolve, reject) => {
            const image = new Image();
            image.onload = () => {
                try {
                    const texture = this.gl.createTexture();
                    this.gl.bindTexture(this.gl.TEXTURE_2D, texture);

                    // Set texture parameters
                    this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_WRAP_S, this.gl.CLAMP_TO_EDGE);
                    this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_WRAP_T, this.gl.CLAMP_TO_EDGE);
                    this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_MIN_FILTER, this.gl.LINEAR);
                    this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_MAG_FILTER, this.gl.LINEAR);

                    // Upload the image to the texture
                    this.gl.texImage2D(this.gl.TEXTURE_2D, 0, this.gl.RGBA, this.gl.RGBA, this.gl.UNSIGNED_BYTE, image);

                    // Store the texture
                    this.textures[name] = {
                        texture: texture,
                        width: image.width,
                        height: image.height
                    };

                    resolve(this.textures[name]);
                } catch (e) {
                    reject(e);
                }
            };
            image.onerror = (e) => {
                reject(e);
            };
            image.src = url;
        });
    }

    /**
     * Create a sprite
     * @param {string} textureName - The name of the texture to use
     * @param {number} x - The x position
     * @param {number} y - The y position
     * @param {number} width - The width
     * @param {number} height - The height
     * @param {Object} options - Additional options
     * @returns {Object} - The sprite object
     */
    createSprite(textureName, x, y, width, height, options = {}) {
        const texture = this.textures[textureName];
        if (!texture) {
            console.error(`Texture '${textureName}' not found`);
            return null;
        }

        const sprite = {
            texture: textureName,
            x: x,
            y: y,
            width: width,
            height: height,
            rotation: options.rotation || 0,
            scale: options.scale || 1,
            color: options.color || [1, 1, 1, 1],
            texCoords: options.texCoords || [0, 0, 1, 1], // [x1, y1, x2, y2]
            visible: options.visible !== undefined ? options.visible : true,
            id: options.id || `sprite_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        };

        this.sprites.push(sprite);
        return sprite;
    }

    /**
     * Remove a sprite
     * @param {string} id - The ID of the sprite to remove
     */
    removeSprite(id) {
        const index = this.sprites.findIndex(sprite => sprite.id === id);
        if (index !== -1) {
            this.sprites.splice(index, 1);
        }
    }

    /**
     * Update a sprite's properties
     * @param {string} id - The ID of the sprite to update
     * @param {Object} properties - The properties to update
     */
    updateSprite(id, properties) {
        const sprite = this.sprites.find(sprite => sprite.id === id);
        if (sprite) {
            Object.assign(sprite, properties);
        }
    }

    /**
     * Set the camera position
     * @param {number} x - The x position
     * @param {number} y - The y position
     */
    setCamera(x, y) {
        this.cameraX = x;
        this.cameraY = y;
    }

    /**
     * Set the zoom scale
     * @param {number} scale - The zoom scale
     */
    setScale(scale) {
        this.scale = scale;
    }

    /**
     * Create a transformation matrix
     * @returns {Float32Array} - The transformation matrix
     */
    createTransformMatrix() {
        // Create a projection matrix for the canvas
        const projectionMatrix = new Float32Array([
            2 / this.canvas.width, 0, 0,
            0, -2 / this.canvas.height, 0,
            -1, 1, 1
        ]);

        // Create a camera matrix
        const cameraMatrix = new Float32Array([
            this.scale, 0, 0,
            0, this.scale, 0,
            -this.cameraX, -this.cameraY, 1
        ]);

        // Multiply the matrices
        return this.multiplyMatrices(projectionMatrix, cameraMatrix);
    }

    /**
     * Multiply two 3x3 matrices
     * @param {Float32Array} a - The first matrix
     * @param {Float32Array} b - The second matrix
     * @returns {Float32Array} - The result matrix
     */
    multiplyMatrices(a, b) {
        const result = new Float32Array(9);

        for (let row = 0; row < 3; row++) {
            for (let col = 0; col < 3; col++) {
                let sum = 0;
                for (let i = 0; i < 3; i++) {
                    sum += a[row * 3 + i] * b[i * 3 + col];
                }
                result[row * 3 + col] = sum;
            }
        }

        return result;
    }

    /**
     * Render the scene
     */
    render() {
        if (!this.gl || !this.program) return;

        // Resize canvas if needed
        this.resizeCanvas();

        // Clear the canvas with the background color
        this.gl.clear(this.gl.COLOR_BUFFER_BIT | this.gl.DEPTH_BUFFER_BIT);

        // Force a full clear to ensure the background color is applied
        this.gl.clearColor(0.5, 0.8, 0.3, 1.0);
        this.gl.clear(this.gl.COLOR_BUFFER_BIT);

        // Use the shader program
        this.gl.useProgram(this.program);

        // Set the transformation matrix
        const matrix = this.createTransformMatrix();
        this.gl.uniformMatrix3fv(this.matrixUniform, false, matrix);

        // Bind the sprite buffer
        this.gl.bindBuffer(this.gl.ARRAY_BUFFER, this.spriteBuffer);

        // Set up attribute pointers
        this.gl.vertexAttribPointer(this.positionAttrib, 2, this.gl.FLOAT, false, 32, 0);
        this.gl.vertexAttribPointer(this.texCoordAttrib, 2, this.gl.FLOAT, false, 32, 8);
        this.gl.vertexAttribPointer(this.colorAttrib, 4, this.gl.FLOAT, false, 32, 16);

        // Render each visible sprite
        this.sprites.forEach(sprite => {
            if (!sprite.visible) return;

            const texture = this.textures[sprite.texture];
            if (!texture) return;

            // Bind the texture
            this.gl.bindTexture(this.gl.TEXTURE_2D, texture.texture);
            this.gl.uniform1i(this.textureUniform, 0);

            // Create vertex data for the sprite
            const vertices = this.createSpriteVertices(sprite);

            // Upload the vertex data
            this.gl.bufferData(this.gl.ARRAY_BUFFER, vertices, this.gl.STATIC_DRAW);

            // Draw the sprite
            this.gl.drawArrays(this.gl.TRIANGLE_STRIP, 0, 4);
        });
    }

    /**
     * Create vertex data for a sprite
     * @param {Object} sprite - The sprite to create vertices for
     * @returns {Float32Array} - The vertex data
     */
    createSpriteVertices(sprite) {
        const x1 = sprite.x;
        const y1 = sprite.y;
        const x2 = sprite.x + sprite.width;
        const y2 = sprite.y + sprite.height;

        const tx1 = sprite.texCoords[0];
        const ty1 = sprite.texCoords[1];
        const tx2 = sprite.texCoords[2];
        const ty2 = sprite.texCoords[3];

        const r = sprite.color[0];
        const g = sprite.color[1];
        const b = sprite.color[2];
        const a = sprite.color[3];

        // Create vertex data (position, texCoord, color)
        return new Float32Array([
            x1, y1, tx1, ty1, r, g, b, a,
            x2, y1, tx2, ty1, r, g, b, a,
            x1, y2, tx1, ty2, r, g, b, a,
            x2, y2, tx2, ty2, r, g, b, a
        ]);
    }
}
