/**
 * Game Renderer for TSTO Web Game
 * Uses WebGL for hardware-accelerated rendering
 */

class GameRenderer {
    /**
     * Initialize the game renderer
     * @param {HTMLCanvasElement} canvas - The canvas element to render to
     */
    constructor(canvas) {
        this.canvas = canvas;
        this.renderer = new WebGLRenderer(canvas);
        this.spriteManager = new SpriteManager(this.renderer);
        this.atlas = null;
        this.buildings = {};
        this.characters = {};
        this.terrain = {};
        this.ui = {};
        this.cameraX = 0;
        this.cameraY = 0;
        this.scale = 1;
        this.dragging = false;
        this.lastMouseX = 0;
        this.lastMouseY = 0;
        this.animationFrameId = null;
        this.lastFrameTime = 0;
        this.frameRate = 60;
        this.frameDuration = 1000 / this.frameRate;

        // Initialize the renderer
        this.init();
    }

    /**
     * Initialize the renderer
     */
    async init() {
        try {
            // Set up event listeners
            this.setupEventListeners();

            // Create the texture atlas
            await this.createTextureAtlas();

            // Create the background grid
            this.createBackgroundGrid();

            // Start the render loop
            this.startRenderLoop();

            console.log('Game renderer initialized successfully');
        } catch (e) {
            console.error('Error initializing game renderer:', e);
        }
    }

    /**
     * Set up event listeners
     */
    setupEventListeners() {
        // Mouse events for camera control
        this.canvas.addEventListener('mousedown', this.onMouseDown.bind(this));
        this.canvas.addEventListener('mousemove', this.onMouseMove.bind(this));
        this.canvas.addEventListener('mouseup', this.onMouseUp.bind(this));
        this.canvas.addEventListener('wheel', this.onWheel.bind(this));

        // Touch events for mobile
        this.canvas.addEventListener('touchstart', this.onTouchStart.bind(this));
        this.canvas.addEventListener('touchmove', this.onTouchMove.bind(this));
        this.canvas.addEventListener('touchend', this.onTouchEnd.bind(this));

        // Window resize event
        window.addEventListener('resize', this.onResize.bind(this));
    }

    /**
     * Create the texture atlas
     */
    async createTextureAtlas() {
        try {
            console.log('Creating texture atlas...');

            // Create a new texture atlas
            this.atlas = new TextureAtlas(this.renderer, 2048, 2048);

            // Create a grass texture immediately
            await this.createPlaceholderGrass();

            // Load building textures
            await this.loadBuildingTextures();

            // Load character textures
            await this.loadCharacterTextures();

            // Load terrain textures
            await this.loadTerrainTextures();

            // Load UI textures
            await this.loadUITextures();

            // Verify grass texture exists
            if (!this.atlas.regions.grass) {
                console.warn('Grass texture not found in atlas after loading, creating it now');
                await this.createPlaceholderGrass();
            }

            console.log('Texture atlas created successfully');
        } catch (e) {
            console.error('Error creating texture atlas:', e);
        }
    }

    /**
     * Load building textures
     */
    async loadBuildingTextures() {
        // Define building textures to load
        const buildingTextures = {
            'house': 'assets/buildings/house.svg',
            'shop': 'assets/buildings/shop.svg',
            'factory': 'assets/buildings/factory.svg',
            'kwik_e_mart': 'assets/buildings/kwik_e_mart.svg',
            'power_plant': 'assets/buildings/power_plant.svg'
        };

        // Load each texture into the atlas
        for (const [name, url] of Object.entries(buildingTextures)) {
            try {
                // Try to load the SVG file
                try {
                    const region = await this.loadSVGTexture(name, url, 64, 64);
                    console.log(`Loaded SVG texture for building: ${name}`);
                } catch (svgError) {
                    console.warn(`Failed to load SVG for ${name}, using placeholder: ${svgError}`);
                    // Fallback to placeholder if SVG loading fails
                    const region = await this.createPlaceholderTexture(name, 64, 64, this.getColorForBuilding(name));
                    console.log(`Created placeholder texture for building: ${name}`);
                }
            } catch (e) {
                console.error(`Error loading building texture ${name}:`, e);
            }
        }
    }

    /**
     * Load character textures
     */
    async loadCharacterTextures() {
        console.log('Loading character textures...');

        // Define character textures to load
        const characterTextures = {
            'homer': 'assets/characters/homer.svg',
            'marge': 'assets/characters/marge.svg',
            'bart': 'assets/characters/bart.svg',
            'lisa': 'assets/characters/lisa.svg',
            'maggie': 'assets/characters/maggie.svg'
        };

        // Load each texture into the atlas
        for (const [name, url] of Object.entries(characterTextures)) {
            try {
                console.log(`Attempting to load character texture: ${name} from ${url}`);

                // Always create a placeholder first to ensure we have something
                const placeholderRegion = await this.createCharacterPlaceholder(name, 64, 64);
                console.log(`Created placeholder texture for character: ${name}`);

                // Try to load the SVG file
                try {
                    const region = await this.loadSVGTexture(name, url, 64, 64);
                    console.log(`Successfully loaded SVG texture for character: ${name}`);
                } catch (svgError) {
                    console.warn(`Failed to load SVG for ${name}, using placeholder: ${svgError}`);
                }
            } catch (e) {
                console.error(`Error loading character texture ${name}:`, e);
            }
        }
    }

    /**
     * Create a character placeholder texture
     * @param {string} name - The name of the character
     * @param {number} width - The width of the texture
     * @param {number} height - The height of the texture
     * @returns {Promise} - A promise that resolves when the texture is created
     */
    async createCharacterPlaceholder(name, width, height) {
        console.log(`Creating character placeholder for ${name} (${width}x${height})`);

        // Create a canvas for the placeholder
        const canvas = document.createElement('canvas');
        canvas.width = width;
        canvas.height = height;
        const ctx = canvas.getContext('2d');

        // Get color for this character
        const color = this.getColorForCharacter(name);

        // Draw a colored circle for the body
        ctx.fillStyle = color;
        ctx.beginPath();
        ctx.arc(width / 2, height / 2, width * 0.4, 0, Math.PI * 2);
        ctx.fill();

        // Draw a smaller circle for the head
        ctx.fillStyle = '#FFE4C4'; // Bisque color for skin
        ctx.beginPath();
        ctx.arc(width / 2, height / 3, width * 0.25, 0, Math.PI * 2);
        ctx.fill();

        // Draw eyes
        ctx.fillStyle = '#FFFFFF';
        ctx.beginPath();
        ctx.arc(width / 2 - width * 0.1, height / 3, width * 0.05, 0, Math.PI * 2);
        ctx.arc(width / 2 + width * 0.1, height / 3, width * 0.05, 0, Math.PI * 2);
        ctx.fill();

        // Draw pupils
        ctx.fillStyle = '#000000';
        ctx.beginPath();
        ctx.arc(width / 2 - width * 0.1, height / 3, width * 0.02, 0, Math.PI * 2);
        ctx.arc(width / 2 + width * 0.1, height / 3, width * 0.02, 0, Math.PI * 2);
        ctx.fill();

        // Draw a smile
        ctx.strokeStyle = '#000000';
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.arc(width / 2, height / 3 + width * 0.1, width * 0.15, 0.1 * Math.PI, 0.9 * Math.PI, false);
        ctx.stroke();

        // Draw a border around the whole character
        ctx.strokeStyle = '#000000';
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.arc(width / 2, height / 2, width * 0.45, 0, Math.PI * 2);
        ctx.stroke();

        // Add the name
        ctx.fillStyle = '#000000';
        ctx.font = `${width * 0.15}px Arial`;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'bottom';
        ctx.fillText(name.charAt(0).toUpperCase() + name.slice(1), width / 2, height - 5);

        // Add the image to the atlas
        return this.atlas.addImage(name, canvas);
    }

    /**
     * Load terrain textures
     */
    async loadTerrainTextures() {
        // Define terrain textures to load
        const terrainTextures = {
            'grass': 'assets/terrain/grass.svg',
            'road': 'assets/terrain/road.svg',
            'water': 'assets/terrain/water.svg',
            'sidewalk': 'assets/terrain/sidewalk.svg'
        };

        // Load each texture into the atlas
        for (const [name, url] of Object.entries(terrainTextures)) {
            try {
                // Try to load the SVG file
                try {
                    const region = await this.loadSVGTexture(name, url, 32, 32);
                    console.log(`Loaded SVG texture for terrain: ${name}`);
                } catch (svgError) {
                    console.warn(`Failed to load SVG for ${name}, using placeholder: ${svgError}`);
                    // Fallback to placeholder if SVG loading fails
                    const region = await this.createPlaceholderTexture(name, 32, 32, this.getColorForTerrain(name));
                    console.log(`Created placeholder texture for terrain: ${name}`);
                }
            } catch (e) {
                console.error(`Error loading terrain texture ${name}:`, e);
            }
        }
    }

    /**
     * Load UI textures
     */
    async loadUITextures() {
        // Define UI textures to load
        const uiTextures = {
            'button': 'assets/ui/button.svg',
            'panel': 'assets/ui/panel.svg',
            'icon_money': 'assets/ui/icon_money.svg',
            'icon_donut': 'assets/ui/icon_donut.svg',
            'icon_xp': 'assets/ui/icon_xp.svg'
        };

        // Load each texture into the atlas
        for (const [name, url] of Object.entries(uiTextures)) {
            try {
                // Try to load the SVG file
                try {
                    const region = await this.loadSVGTexture(name, url, 32, 32);
                    console.log(`Loaded SVG texture for UI: ${name}`);
                } catch (svgError) {
                    console.warn(`Failed to load SVG for ${name}, using placeholder: ${svgError}`);
                    // Fallback to placeholder if SVG loading fails
                    const region = await this.createPlaceholderTexture(name, 32, 32, this.getColorForUI(name));
                    console.log(`Created placeholder texture for UI: ${name}`);
                }
            } catch (e) {
                console.error(`Error loading UI texture ${name}:`, e);
            }
        }
    }

    /**
     * Load an SVG texture
     * @param {string} name - The name of the texture
     * @param {string} url - The URL of the SVG file
     * @param {number} width - The width to render the SVG
     * @param {number} height - The height to render the SVG
     * @returns {Promise} - A promise that resolves when the texture is loaded
     */
    async loadSVGTexture(name, url, width, height) {
        return new Promise((resolve, reject) => {
            // Create an image element to load the SVG
            const img = new Image();
            img.onload = () => {
                try {
                    // Create a canvas to render the SVG
                    const canvas = document.createElement('canvas');
                    canvas.width = width;
                    canvas.height = height;
                    const ctx = canvas.getContext('2d');

                    // Draw the SVG to the canvas
                    ctx.drawImage(img, 0, 0, width, height);

                    // Add the image to the atlas
                    this.atlas.addImage(name, canvas)
                        .then(resolve)
                        .catch(reject);
                } catch (e) {
                    reject(e);
                }
            };
            img.onerror = reject;
            img.src = url;
        });
    }

    /**
     * Create a placeholder texture
     * @param {string} name - The name of the texture
     * @param {number} width - The width of the texture
     * @param {number} height - The height of the texture
     * @param {string} color - The color of the texture
     * @returns {Promise} - A promise that resolves when the texture is created
     */
    async createPlaceholderTexture(name, width, height, color) {
        // Create a canvas for the placeholder
        const canvas = document.createElement('canvas');
        canvas.width = width;
        canvas.height = height;
        const ctx = canvas.getContext('2d');

        // Draw a colored rectangle
        ctx.fillStyle = color;
        ctx.fillRect(0, 0, width, height);

        // Draw a border
        ctx.strokeStyle = '#000000';
        ctx.lineWidth = 2;
        ctx.strokeRect(1, 1, width - 2, height - 2);

        // Draw the name
        ctx.fillStyle = '#ffffff';
        ctx.font = '10px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(name, width / 2, height / 2);

        // Add the image to the atlas
        return this.atlas.addImage(name, canvas);
    }

    /**
     * Get a color for a building
     * @param {string} name - The name of the building
     * @returns {string} - The color
     */
    getColorForBuilding(name) {
        const colors = {
            'house': '#8B4513',
            'shop': '#4682B4',
            'factory': '#708090',
            'kwik_e_mart': '#32CD32',
            'power_plant': '#B22222'
        };

        return colors[name] || '#A0522D';
    }

    /**
     * Get a color for a character
     * @param {string} name - The name of the character
     * @returns {string} - The color
     */
    getColorForCharacter(name) {
        const colors = {
            'homer': '#FFD700',
            'marge': '#1E90FF',
            'bart': '#FF4500',
            'lisa': '#FF69B4',
            'maggie': '#9370DB'
        };

        return colors[name] || '#FFA07A';
    }

    /**
     * Get a color for terrain
     * @param {string} name - The name of the terrain
     * @returns {string} - The color
     */
    getColorForTerrain(name) {
        const colors = {
            'grass': '#7CFC00',
            'road': '#696969',
            'water': '#1E90FF',
            'sidewalk': '#D3D3D3'
        };

        return colors[name] || '#8FBC8F';
    }

    /**
     * Get a color for UI
     * @param {string} name - The name of the UI element
     * @returns {string} - The color
     */
    getColorForUI(name) {
        const colors = {
            'button': '#4682B4',
            'panel': '#778899',
            'icon_money': '#FFD700',
            'icon_donut': '#FF69B4',
            'icon_xp': '#32CD32'
        };

        return colors[name] || '#A9A9A9';
    }

    /**
     * Start the render loop
     */
    startRenderLoop() {
        // Cancel any existing animation frame
        if (this.animationFrameId) {
            cancelAnimationFrame(this.animationFrameId);
        }

        // Start the render loop
        this.lastFrameTime = performance.now();
        this.animationFrameId = requestAnimationFrame(this.renderLoop.bind(this));
    }

    /**
     * Stop the render loop
     */
    stopRenderLoop() {
        if (this.animationFrameId) {
            cancelAnimationFrame(this.animationFrameId);
            this.animationFrameId = null;
        }
    }

    /**
     * The render loop
     * @param {number} timestamp - The current timestamp
     */
    renderLoop(timestamp) {
        // Calculate delta time
        const deltaTime = timestamp - this.lastFrameTime;

        // Check if it's time to render a new frame
        if (deltaTime >= this.frameDuration) {
            // Update the last frame time
            this.lastFrameTime = timestamp - (deltaTime % this.frameDuration);

            // Update animations
            this.spriteManager.updateAnimations();

            // Update the camera
            this.renderer.setCamera(this.cameraX, this.cameraY);
            this.renderer.setScale(this.scale);

            // Render the scene
            this.renderer.render();
        }

        // Continue the render loop
        this.animationFrameId = requestAnimationFrame(this.renderLoop.bind(this));
    }

    /**
     * Handle mouse down event
     * @param {MouseEvent} event - The mouse event
     */
    onMouseDown(event) {
        this.dragging = true;
        this.lastMouseX = event.clientX;
        this.lastMouseY = event.clientY;
        this.canvas.style.cursor = 'grabbing';
    }

    /**
     * Handle mouse move event
     * @param {MouseEvent} event - The mouse event
     */
    onMouseMove(event) {
        if (this.dragging) {
            const dx = event.clientX - this.lastMouseX;
            const dy = event.clientY - this.lastMouseY;

            this.cameraX -= dx / this.scale;
            this.cameraY -= dy / this.scale;

            this.lastMouseX = event.clientX;
            this.lastMouseY = event.clientY;
        }
    }

    /**
     * Handle mouse up event
     * @param {MouseEvent} event - The mouse event
     */
    onMouseUp(event) {
        this.dragging = false;
        this.canvas.style.cursor = 'grab';
    }

    /**
     * Handle wheel event
     * @param {WheelEvent} event - The wheel event
     */
    onWheel(event) {
        event.preventDefault();

        // Calculate the zoom factor
        const zoomFactor = event.deltaY > 0 ? 0.9 : 1.1;

        // Calculate the mouse position relative to the canvas
        const rect = this.canvas.getBoundingClientRect();
        const mouseX = event.clientX - rect.left;
        const mouseY = event.clientY - rect.top;

        // Calculate the world position of the mouse
        const worldX = this.cameraX + mouseX / this.scale;
        const worldY = this.cameraY + mouseY / this.scale;

        // Apply the zoom
        this.scale *= zoomFactor;

        // Limit the zoom
        this.scale = Math.max(0.1, Math.min(10, this.scale));

        // Adjust the camera to zoom toward the mouse position
        this.cameraX = worldX - mouseX / this.scale;
        this.cameraY = worldY - mouseY / this.scale;
    }

    /**
     * Handle touch start event
     * @param {TouchEvent} event - The touch event
     */
    onTouchStart(event) {
        if (event.touches.length === 1) {
            // Single touch - start dragging
            this.dragging = true;
            this.lastMouseX = event.touches[0].clientX;
            this.lastMouseY = event.touches[0].clientY;
        }
    }

    /**
     * Handle touch move event
     * @param {TouchEvent} event - The touch event
     */
    onTouchMove(event) {
        if (this.dragging && event.touches.length === 1) {
            // Single touch - drag
            const dx = event.touches[0].clientX - this.lastMouseX;
            const dy = event.touches[0].clientY - this.lastMouseY;

            this.cameraX -= dx / this.scale;
            this.cameraY -= dy / this.scale;

            this.lastMouseX = event.touches[0].clientX;
            this.lastMouseY = event.touches[0].clientY;
        }
    }

    /**
     * Handle touch end event
     * @param {TouchEvent} event - The touch event
     */
    onTouchEnd(event) {
        this.dragging = false;
    }

    /**
     * Handle resize event
     */
    onResize() {
        // Resize the canvas
        this.renderer.resizeCanvas();
    }

    /**
     * Add a building to the scene
     * @param {string} id - The ID of the building
     * @param {string} type - The type of the building
     * @param {number} x - The x position
     * @param {number} y - The y position
     * @param {Object} options - Additional options
     * @returns {Object} - The building object
     */
    addBuilding(id, type, x, y, options = {}) {
        // Create a sprite for the building
        const sprite = this.spriteManager.createSpriteFromAtlas(id, this.atlas, type, x, y, options);

        if (sprite) {
            // Store the building
            this.buildings[id] = {
                id: id,
                type: type,
                sprite: sprite,
                x: x,
                y: y,
                width: sprite.width,
                height: sprite.height,
                ...options
            };
        }

        return this.buildings[id];
    }

    /**
     * Remove a building from the scene
     * @param {string} id - The ID of the building
     */
    removeBuilding(id) {
        const building = this.buildings[id];
        if (building) {
            // Remove the sprite
            this.spriteManager.removeSprite(id);

            // Remove the building
            delete this.buildings[id];
        }
    }

    /**
     * Add a character to the scene
     * @param {string} id - The ID of the character
     * @param {string} type - The type of the character
     * @param {number} x - The x position
     * @param {number} y - The y position
     * @param {Object} options - Additional options
     * @returns {Object} - The character object
     */
    addCharacter(id, type, x, y, options = {}) {
        // Create a sprite for the character
        const sprite = this.spriteManager.createSpriteFromAtlas(id, this.atlas, type, x, y, options);

        if (sprite) {
            // Store the character
            this.characters[id] = {
                id: id,
                type: type,
                sprite: sprite,
                x: x,
                y: y,
                width: sprite.width,
                height: sprite.height,
                ...options
            };
        }

        return this.characters[id];
    }

    /**
     * Remove a character from the scene
     * @param {string} id - The ID of the character
     */
    removeCharacter(id) {
        const character = this.characters[id];
        if (character) {
            // Remove the sprite
            this.spriteManager.removeSprite(id);

            // Remove the character
            delete this.characters[id];
        }
    }

    /**
     * Create a background grid of grass tiles
     */
    createBackgroundGrid() {
        try {
            console.log('Creating background grid...');

            // Define grid size
            const gridSize = 32; // Size of each grid cell
            const gridWidth = 50; // Number of cells horizontally
            const gridHeight = 50; // Number of cells vertically

            // Calculate the total size
            const totalWidth = gridWidth * gridSize;
            const totalHeight = gridHeight * gridSize;

            // Center the grid
            const startX = -totalWidth / 2;
            const startY = -totalHeight / 2;

            // Create a solid background color first
            this.createSolidBackground();

            // Wait for the atlas to be ready
            if (!this.atlas || !this.atlas.regions || !this.atlas.regions['grass']) {
                console.warn('Grass texture not found in atlas, creating placeholder grass');
                // Create a placeholder grass texture if it doesn't exist
                this.createPlaceholderGrass();
            }

            // Create grass tiles
            for (let row = 0; row < gridHeight; row++) {
                for (let col = 0; col < gridWidth; col++) {
                    const x = startX + col * gridSize;
                    const y = startY + row * gridSize;

                    // Create a unique ID for this tile
                    const tileId = `grass_${row}_${col}`;

                    // Add slight color variation to make it look more natural
                    const colorVariation = [
                        0.9 + Math.random() * 0.2, // Slight red variation
                        0.9 + Math.random() * 0.2, // Slight green variation
                        0.9 + Math.random() * 0.2, // Slight blue variation
                        1.0 // Full alpha
                    ];

                    // Create the grass sprite
                    const sprite = this.spriteManager.createSpriteFromAtlas(
                        tileId,
                        this.atlas,
                        'grass',
                        x,
                        y,
                        {
                            color: colorVariation,
                            zIndex: -100 // Put it behind everything else
                        }
                    );

                    // Store the terrain tile
                    if (sprite) {
                        this.terrain[tileId] = {
                            id: tileId,
                            type: 'grass',
                            sprite: sprite,
                            x: x,
                            y: y,
                            width: gridSize,
                            height: gridSize
                        };
                    }
                }
            }

            console.log(`Background grid created with ${gridWidth}x${gridHeight} tiles`);
        } catch (e) {
            console.error('Error creating background grid:', e);
        }
    }

    /**
     * Create a solid background color as a fallback
     */
    createSolidBackground() {
        try {
            console.log('Creating solid background...');

            // Get canvas dimensions
            const width = this.canvas.width;
            const height = this.canvas.height;

            // Create a canvas for the background
            const canvas = document.createElement('canvas');
            canvas.width = 64;
            canvas.height = 64;
            const ctx = canvas.getContext('2d');

            // Fill with a grass green color
            ctx.fillStyle = '#7CFC00';
            ctx.fillRect(0, 0, 64, 64);

            // Add some texture/pattern to make it look like grass
            ctx.fillStyle = '#8FBC8F';
            for (let i = 0; i < 100; i++) {
                const x = Math.random() * 64;
                const y = Math.random() * 64;
                const size = 1 + Math.random() * 3;
                ctx.fillRect(x, y, size, size);
            }

            // Add the background to the atlas
            this.atlas.addImage('background', canvas)
                .then(() => {
                    console.log('Background texture added to atlas');

                    // Create a full-screen sprite with the background texture
                    const sprite = this.spriteManager.createSpriteFromAtlas(
                        'background_sprite',
                        this.atlas,
                        'background',
                        -width * 5,
                        -height * 5,
                        {
                            width: width * 10,
                            height: height * 10,
                            zIndex: -200 // Put it behind everything else
                        }
                    );

                    if (sprite) {
                        console.log('Background sprite created successfully');
                    }
                })
                .catch(e => {
                    console.error('Error adding background to atlas:', e);
                });
        } catch (e) {
            console.error('Error creating solid background:', e);
        }
    }

    /**
     * Create a placeholder grass texture if the real one isn't available
     */
    createPlaceholderGrass() {
        try {
            console.log('Creating placeholder grass texture...');

            // Create a canvas for the grass
            const canvas = document.createElement('canvas');
            canvas.width = 32;
            canvas.height = 32;
            const ctx = canvas.getContext('2d');

            // Fill with a grass green color
            ctx.fillStyle = '#7CFC00';
            ctx.fillRect(0, 0, 32, 32);

            // Add some texture/pattern to make it look like grass
            ctx.fillStyle = '#8FBC8F';
            for (let i = 0; i < 20; i++) {
                const x = Math.random() * 32;
                const y = Math.random() * 32;
                const size = 1 + Math.random() * 2;
                ctx.fillRect(x, y, size, size);
            }

            // Add the grass to the atlas
            this.atlas.addImage('grass', canvas)
                .then(() => {
                    console.log('Placeholder grass texture added to atlas');
                })
                .catch(e => {
                    console.error('Error adding placeholder grass to atlas:', e);
                });
        } catch (e) {
            console.error('Error creating placeholder grass texture:', e);
        }
    }
}
