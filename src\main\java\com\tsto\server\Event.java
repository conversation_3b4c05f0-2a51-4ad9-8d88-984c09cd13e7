package com.tsto.server;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class Event {
    private String id;
    private String name;
    private String description;
    private long startTime;
    private long endTime;
    private boolean isActive;
    private List<Quest> quests;
    private Map<String, Integer> rewards;

    public Event(String id, String name, String description, long startTime, long endTime) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.startTime = startTime;
        this.endTime = endTime;
        this.isActive = false;
        this.quests = new ArrayList<>();
        this.rewards = new HashMap<>();
    }

    public String getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public String getDescription() {
        return description;
    }

    public long getStartTime() {
        return startTime;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    public long getEndTime() {
        return endTime;
    }

    public void setEndTime(long endTime) {
        this.endTime = endTime;
    }

    public boolean isActive() {
        return isActive;
    }

    public void setActive(boolean active) {
        this.isActive = active;
    }

    public List<Quest> getQuests() {
        return new ArrayList<>(quests);
    }

    public void addQuest(Quest quest) {
        quests.add(quest);
    }

    public void removeQuest(Quest quest) {
        quests.remove(quest);
    }

    public Map<String, Integer> getRewards() {
        return new HashMap<>(rewards);
    }

    public void addReward(String type, int amount) {
        rewards.put(type, amount);
    }

    public boolean isInProgress(long currentTime) {
        return currentTime >= startTime && currentTime <= endTime;
    }

    public boolean hasExpired(long currentTime) {
        return currentTime > endTime;
    }
} 