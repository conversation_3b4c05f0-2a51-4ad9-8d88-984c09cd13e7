<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TSTO - Shop</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <div class="game-header">
            <div class="player-info">
                <span id="player-name"></span>
                <span id="player-money">$1000</span>
                <span id="player-donuts">10</span>
            </div>
            <div class="game-controls">
                <button class="save-btn">Save Game</button>
                <button class="load-btn">Load Game</button>
                <button class="logout-btn">Logout</button>
                <button class="back-btn">Back to Main</button>
            </div>
        </div>
        
        <div class="shop-container">
            <div class="shop-sidebar">
                <div class="shop-categories">
                    <h3>Shop Categories</h3>
                    <div class="category-list">
                        <div class="category-item active" data-category="characters">Characters</div>
                        <div class="category-item" data-category="decorations">Decorations</div>
                        <div class="category-item" data-category="boosts">Boosts</div>
                        <div class="category-item" data-category="premium">Premium</div>
                    </div>
                </div>
            </div>

            <div class="shop-main">
                <div class="shop-items" id="shopItems">
                    <!-- Shop items will be populated by JavaScript -->
                </div>
            </div>
        </div>
    </div>

    <script>
        // Game state
        let gameState = {
            money: 1000,
            donuts: 10,
            lastSave: Date.now()
        };

        // Shop items
        const SHOP_ITEMS = {
            characters: {
                bart: { 
                    name: 'Bart Simpson', 
                    cost: 500, 
                    currency: 'money',
                    description: 'Add Bart to your town',
                    type: 'character'
                },
                lisa: { 
                    name: 'Lisa Simpson', 
                    cost: 750, 
                    currency: 'money',
                    description: 'Add Lisa to your town',
                    type: 'character'
                },
                maggie: { 
                    name: 'Maggie Simpson', 
                    cost: 1000, 
                    currency: 'money',
                    description: 'Add Maggie to your town',
                    type: 'character'
                }
            },
            decorations: {
                tree: { 
                    name: 'Tree', 
                    cost: 100, 
                    currency: 'money',
                    description: 'A beautiful tree for your town',
                    type: 'decoration'
                },
                fountain: { 
                    name: 'Fountain', 
                    cost: 300, 
                    currency: 'money',
                    description: 'A decorative fountain',
                    type: 'decoration'
                },
                statue: { 
                    name: 'Jebediah Statue', 
                    cost: 500, 
                    currency: 'money',
                    description: 'A statue of Springfield\'s founder',
                    type: 'decoration'
                }
            },
            boosts: {
                speed_boost: { 
                    name: 'Speed Boost', 
                    cost: 50, 
                    currency: 'donuts',
                    description: '2x speed for 1 hour',
                    type: 'boost',
                    duration: 3600
                },
                income_boost: { 
                    name: 'Income Boost', 
                    cost: 100, 
                    currency: 'donuts',
                    description: '2x income for 1 hour',
                    type: 'boost',
                    duration: 3600
                },
                xp_boost: { 
                    name: 'XP Boost', 
                    cost: 75, 
                    currency: 'donuts',
                    description: '2x XP for 1 hour',
                    type: 'boost',
                    duration: 3600
                }
            },
            premium: {
                premium_house: { 
                    name: 'Premium House', 
                    cost: 1000, 
                    currency: 'donuts',
                    description: 'A premium house with 2x income',
                    type: 'building'
                },
                premium_store: { 
                    name: 'Premium Store', 
                    cost: 2000, 
                    currency: 'donuts',
                    description: 'A premium store with 2x income',
                    type: 'building'
                },
                premium_factory: { 
                    name: 'Premium Factory', 
                    cost: 3000, 
                    currency: 'donuts',
                    description: 'A premium factory with 2x income',
                    type: 'building'
                }
            }
        };

        let currentCategory = 'characters';

        // Initialize the shop
        function initializeShop() {
            try {
                // Load game state
                if (!loadGameState()) {
                    showNotification('Starting new game...', 'info');
                }
                
                // Initialize UI
                updatePlayerStats();
                updateShopItems();
                
                // Add event listeners
                setupEventListeners();
                
                showNotification('Shop initialized successfully!', 'success');
            } catch (e) {
                console.error('Error initializing shop:', e);
                showNotification('Error initializing shop!', 'error');
            }
        }

        // Setup event listeners
        function setupEventListeners() {
            // Category switching
            document.querySelectorAll('.category-item').forEach(item => {
                item.addEventListener('click', () => {
                    document.querySelector('.category-item.active').classList.remove('active');
                    item.classList.add('active');
                    currentCategory = item.dataset.category;
                    updateShopItems();
                });
            });

            // Game controls
            document.querySelector('.save-btn').addEventListener('click', saveGame);
            document.querySelector('.load-btn').addEventListener('click', loadGame);
            document.querySelector('.logout-btn').addEventListener('click', logout);
            document.querySelector('.back-btn').addEventListener('click', () => {
                window.location.href = 'index.html';
            });
        }

        // Update player stats
        function updatePlayerStats() {
            document.getElementById('player-money').textContent = `$${gameState.money}`;
            document.getElementById('player-donuts').textContent = gameState.donuts;
            document.getElementById('player-name').textContent = localStorage.getItem('tsto_username') || 'Player';
        }

        // Update shop items
        function updateShopItems() {
            const shopItems = document.getElementById('shopItems');
            shopItems.innerHTML = '';
            
            Object.entries(SHOP_ITEMS[currentCategory]).forEach(([id, item]) => {
                const itemDiv = document.createElement('div');
                itemDiv.className = 'shop-item';
                itemDiv.innerHTML = `
                    <div class="shop-item-header">
                        <h3>${item.name}</h3>
                        <span class="item-cost">${item.currency === 'money' ? '$' : ''}${item.cost}${item.currency === 'donuts' ? ' donuts' : ''}</span>
                    </div>
                    <p class="item-description">${item.description}</p>
                    <button class="buy-btn" onclick="buyItem('${id}')" 
                            ${(item.currency === 'money' && gameState.money < item.cost) || 
                              (item.currency === 'donuts' && gameState.donuts < item.cost) ? 'disabled' : ''}>
                        Buy
                    </button>
                `;
                shopItems.appendChild(itemDiv);
            });
        }

        // Buy item
        function buyItem(itemId) {
            const item = SHOP_ITEMS[currentCategory][itemId];
            if (!item) return;
            
            const currency = item.currency;
            if (gameState[currency] >= item.cost) {
                gameState[currency] -= item.cost;
                
                // Handle different item types
                switch (item.type) {
                    case 'character':
                        showNotification(`Added ${item.name} to your town!`, 'success');
                        break;
                    case 'decoration':
                        showNotification(`Added ${item.name} to your inventory!`, 'success');
                        break;
                    case 'boost':
                        showNotification(`${item.name} activated!`, 'success');
                        break;
                    case 'building':
                        showNotification(`Added ${item.name} to your inventory!`, 'success');
                        break;
                }
                
                updatePlayerStats();
                saveGame();
            } else {
                showNotification(`Not enough ${currency}!`, 'error');
            }
        }

        // Save game
        function saveGame() {
            try {
                const saveData = {
                    gameState: gameState,
                    currentUser: {
                        username: localStorage.getItem('tsto_username')
                    },
                    timestamp: Date.now()
                };
                localStorage.setItem('tsto_save', JSON.stringify(saveData));
                gameState.lastSave = Date.now();
                showNotification('Game saved successfully!', 'success');
                return true;
            } catch (e) {
                console.error('Error saving game:', e);
                showNotification('Error saving game!', 'error');
                return false;
            }
        }

        // Load game
        function loadGame() {
            if (loadGameState()) {
                showNotification('Game loaded successfully!', 'success');
            } else {
                showNotification('No valid save game found!', 'error');
            }
        }

        // Load game state
        function loadGameState() {
            try {
                const savedGame = localStorage.getItem('tsto_save');
                if (savedGame) {
                    const loadedData = JSON.parse(savedGame);
                    if (loadedData.gameState) {
                        if (typeof loadedData.gameState.money === 'number' &&
                            typeof loadedData.gameState.donuts === 'number') {
                            gameState = loadedData.gameState;
                            return true;
                        }
                    }
                }
            } catch (e) {
                console.error('Error loading game state:', e);
            }
            return false;
        }

        // Show notification
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        // Logout
        function logout() {
            localStorage.removeItem('tsto_username');
            window.location.href = 'index.html';
        }

        // Initialize the shop when the page loads
        document.addEventListener('DOMContentLoaded', initializeShop);
    </script>
</body>
</html> 