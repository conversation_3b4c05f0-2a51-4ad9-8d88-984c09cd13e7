/**
 * WebGL Texture Atlas for TSTO Web Game
 * Provides efficient texture management for WebGL rendering
 */

class TextureAtlas {
    /**
     * Initialize the texture atlas
     * @param {WebGLRenderer} renderer - The WebGL renderer
     * @param {number} width - The width of the atlas
     * @param {number} height - The height of the atlas
     */
    constructor(renderer, width, height) {
        this.renderer = renderer;
        this.width = width;
        this.height = height;
        this.gl = renderer.gl;
        this.canvas = document.createElement('canvas');
        this.canvas.width = width;
        this.canvas.height = height;
        this.ctx = this.canvas.getContext('2d');
        this.regions = {};
        this.texture = null;
        this.name = `atlas_${Date.now()}`;

        // Clear the canvas
        this.ctx.clearRect(0, 0, width, height);

        // Create the texture
        this.createTexture();

        // Create default textures
        this.createDefaultTextures();
    }

    /**
     * Create default textures for the game
     */
    async createDefaultTextures() {
        try {
            // Create a grass texture
            await this.createGrassTexture();
        } catch (e) {
            console.error('Error creating default textures:', e);
        }
    }

    /**
     * Create a grass texture
     */
    async createGrassTexture() {
        try {
            console.log('Creating grass texture...');

            // Create a canvas for the grass
            const canvas = document.createElement('canvas');
            canvas.width = 32;
            canvas.height = 32;
            const ctx = canvas.getContext('2d');

            // Fill with a grass green color
            ctx.fillStyle = '#7CFC00';
            ctx.fillRect(0, 0, 32, 32);

            // Add some texture/pattern to make it look like grass
            ctx.fillStyle = '#8FBC8F';
            for (let i = 0; i < 20; i++) {
                const x = Math.random() * 32;
                const y = Math.random() * 32;
                const size = 1 + Math.random() * 2;
                ctx.fillRect(x, y, size, size);
            }

            // Add some lighter spots
            ctx.fillStyle = '#ADFF2F';
            for (let i = 0; i < 10; i++) {
                const x = Math.random() * 32;
                const y = Math.random() * 32;
                const size = 1 + Math.random() * 2;
                ctx.fillRect(x, y, size, size);
            }

            // Add the grass to the atlas
            await this.addImage('grass', canvas);
            console.log('Grass texture created successfully');
        } catch (e) {
            console.error('Error creating grass texture:', e);
        }
    }

    /**
     * Create the WebGL texture
     */
    createTexture() {
        this.texture = this.gl.createTexture();
        this.gl.bindTexture(this.gl.TEXTURE_2D, this.texture);

        // Set texture parameters
        this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_WRAP_S, this.gl.CLAMP_TO_EDGE);
        this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_WRAP_T, this.gl.CLAMP_TO_EDGE);
        this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_MIN_FILTER, this.gl.LINEAR);
        this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_MAG_FILTER, this.gl.LINEAR);

        // Upload the empty canvas to the texture
        this.gl.texImage2D(this.gl.TEXTURE_2D, 0, this.gl.RGBA, this.gl.RGBA, this.gl.UNSIGNED_BYTE, this.canvas);

        // Store the texture in the renderer
        this.renderer.textures[this.name] = {
            texture: this.texture,
            width: this.width,
            height: this.height,
            isAtlas: true
        };
    }

    /**
     * Add an image to the atlas
     * @param {string} name - The name of the image
     * @param {HTMLImageElement|string} image - The image or image URL
     * @returns {Promise} - A promise that resolves when the image is added
     */
    addImage(name, image) {
        return new Promise((resolve, reject) => {
            const loadImage = (img) => {
                try {
                    // Find a position for the image using a simple packing algorithm
                    const position = this.findPosition(img.width, img.height);
                    if (!position) {
                        reject(new Error('Not enough space in the atlas'));
                        return;
                    }

                    // Draw the image to the atlas
                    this.ctx.drawImage(img, position.x, position.y);

                    // Store the region
                    this.regions[name] = {
                        x: position.x,
                        y: position.y,
                        width: img.width,
                        height: img.height,
                        texCoords: [
                            position.x / this.width,
                            position.y / this.height,
                            (position.x + img.width) / this.width,
                            (position.y + img.height) / this.height
                        ]
                    };

                    // Update the texture
                    this.updateTexture();

                    resolve(this.regions[name]);
                } catch (e) {
                    reject(e);
                }
            };

            if (typeof image === 'string') {
                // Load the image from URL
                const img = new Image();
                img.onload = () => loadImage(img);
                img.onerror = reject;
                img.src = image;
            } else {
                // Use the provided image
                loadImage(image);
            }
        });
    }

    /**
     * Find a position for an image in the atlas
     * @param {number} width - The width of the image
     * @param {number} height - The height of the image
     * @returns {Object|null} - The position or null if no space is available
     */
    findPosition(width, height) {
        // Simple packing algorithm: find the first position where the image fits
        // This could be improved with more sophisticated packing algorithms

        // Get all used regions
        const usedRegions = Object.values(this.regions);

        // Try to find a position
        for (let y = 0; y <= this.height - height; y += 1) {
            for (let x = 0; x <= this.width - width; x += 1) {
                // Check if the position is valid
                let valid = true;

                for (const region of usedRegions) {
                    // Check if the regions overlap
                    if (
                        x < region.x + region.width &&
                        x + width > region.x &&
                        y < region.y + region.height &&
                        y + height > region.y
                    ) {
                        valid = false;
                        break;
                    }
                }

                if (valid) {
                    return { x, y };
                }
            }
        }

        return null;
    }

    /**
     * Update the WebGL texture
     */
    updateTexture() {
        this.gl.bindTexture(this.gl.TEXTURE_2D, this.texture);
        this.gl.texImage2D(this.gl.TEXTURE_2D, 0, this.gl.RGBA, this.gl.RGBA, this.gl.UNSIGNED_BYTE, this.canvas);
    }

    /**
     * Create a sprite from a region in the atlas
     * @param {string} regionName - The name of the region
     * @param {number} x - The x position
     * @param {number} y - The y position
     * @param {Object} options - Additional options
     * @returns {Object} - The sprite object
     */
    createSprite(regionName, x, y, options = {}) {
        const region = this.regions[regionName];
        if (!region) {
            console.error(`Region '${regionName}' not found in atlas`);
            return null;
        }

        return this.renderer.createSprite(this.name, x, y, region.width, region.height, {
            ...options,
            texCoords: region.texCoords
        });
    }
}
