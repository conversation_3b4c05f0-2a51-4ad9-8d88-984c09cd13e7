<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TSTO WEB EDITION</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <h1>TSTO WEB EDITION</h1>
        <div id="loginForm">
            <h2>Login</h2>
            <form id="login-form">
                <div class="form-group">
                    <label for="login-username">Username:</label>
                    <input type="text" id="login-username" required>
                </div>
                <div class="form-group">
                    <label for="login-password">Password:</label>
                    <input type="password" id="login-password" required>
                </div>
                <button type="submit">Login</button>
                <button type="button" onclick="showRegister()">Register</button>
            </form>
        </div>

        <div id="registerForm" style="display: none;">
            <h2>Register</h2>
            <form id="register-form">
                <div class="form-group">
                    <label for="regUsername">Username:</label>
                    <input type="text" id="regUsername" required>
                </div>
                <div class="form-group">
                    <label for="regPassword">Password:</label>
                    <input type="password" id="regPassword" required>
                </div>
                <button type="submit">Register</button>
                <button type="button" onclick="showLogin()">Back to Login</button>
            </form>
        </div>

        <div id="gameArea" style="display: none;">
            <div class="game-header">
                <div class="player-info">
                    <span id="player-name"></span>
                    <span id="player-money">$1000</span>
                    <span id="player-donuts">10</span>
                </div>
                <div class="game-controls">
                    <button class="save-btn" onclick="saveGame()">Save Game</button>
                    <button class="load-btn" onclick="loadGame()">Load Game</button>
                    <button class="logout-btn" onclick="logout()">Logout</button>
                </div>
            </div>

            <div class="game-content">
                <div class="town-view">
                    <h2>Your Town</h2>
                    <div class="town-canvas" id="townCanvas">
                        <div class="town-district district-residential">
                            <h3>Residential District</h3>
                            <div class="district-content" id="residentialDistrict"></div>
                        </div>
                        <div class="town-district district-commercial">
                            <h3>Commercial District</h3>
                            <div class="district-content" id="commercialDistrict"></div>
                        </div>
                        <div class="town-district district-industrial">
                            <h3>Industrial District</h3>
                            <div class="district-content" id="industrialDistrict"></div>
                        </div>
                        <div class="town-district district-entertainment">
                            <h3>Entertainment District</h3>
                            <div class="district-content" id="entertainmentDistrict"></div>
                        </div>
                        <div class="town-district district-education">
                            <h3>Education District</h3>
                            <div class="district-content" id="educationDistrict"></div>
                        </div>
                        <div class="town-district district-nature">
                            <h3>Nature District</h3>
                            <div class="district-content" id="natureDistrict"></div>
                        </div>
                    </div>
                </div>

                <div class="game-menu">
                    <div class="menu-section">
                        <h3>Game Options</h3>
                        <div class="navigation-list">
                            <a href="town.html" class="nav-item">
                                <span>Build Your Town</span>
                                <span>→</span>
                            </a>
                            <a href="town-webgl.html" class="nav-item">
                                <span>Build Your Town (WebGL)</span>
                                <span>→</span>
                            </a>
                            <a href="characters.html" class="nav-item">
                                <span>Characters</span>
                                <span>→</span>
                            </a>
                            <a href="quests.html" class="nav-item">
                                <span>Quests</span>
                                <span>→</span>
                            </a>
                            <a href="shop.html" class="nav-item">
                                <span>Shop</span>
                                <span>→</span>
                            </a>
                        </div>
                    </div>

                    <div class="menu-section">
                        <h3>Buildings</h3>
                        <div class="buildings-list" id="buildingsList">
                            <div class="building-item" onclick="placeBuilding('house')">
                                <span>House</span>
                                <span>$500</span>
                            </div>
                            <div class="building-item" onclick="placeBuilding('store')">
                                <span>Store</span>
                                <span>$1000</span>
                            </div>
                            <div class="building-item" onclick="placeBuilding('factory')">
                                <span>Factory</span>
                                <span>$2000</span>
                            </div>
                        </div>
                    </div>

                    <div class="menu-section">
                        <h3>Characters</h3>
                        <div class="characters-list" id="charactersList">
                            <!-- Characters will be populated by JavaScript -->
                        </div>
                    </div>

                    <div class="menu-section">
                        <h3>Quests</h3>
                        <div class="quests-list" id="questsList">
                            <!-- Quests will be populated by JavaScript -->
                        </div>
                    </div>

                    <div class="menu-section">
                        <h3>Achievements</h3>
                        <div class="achievements-list" id="achievementsList">
                            <!-- Achievements will be populated by JavaScript -->
                        </div>
                    </div>

                    <div class="menu-section">
                        <h3>Shop</h3>
                        <div class="shop-items" id="shopItems">
                            <!-- Shop items will be populated by JavaScript -->
                        </div>
                    </div>

                    <div class="menu-section">
                        <h3>Research</h3>
                        <div class="research-header">
                            <span>Research Points: <span id="researchPoints">0</span></span>
                        </div>
                        <div class="research-list" id="researchList">
                            <!-- Research items will be populated by JavaScript -->
                        </div>
                    </div>

                    <div class="menu-section">
                        <h3>Mini-Games</h3>
                        <div class="mini-games-list" id="miniGamesList">
                            <!-- Mini-games will be populated by JavaScript -->
                        </div>
                    </div>

                    <div class="menu-section">
                        <h3>Trading</h3>
                        <div class="trading-list" id="tradingList">
                            <!-- Trading items will be populated by JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE_URL = window.location.origin;
        let currentUser = null;
        let selectedBuilding = null;
        let movingBuilding = null;
        let townGrid = [];
        let gameState = {
            money: 1000,
            donuts: 10,
            level: 1,
            xp: 0,
            xpToNextLevel: 100,
            lastDailyReward: null,
            dailyRewardStreak: 0,
            weather: {
                current: 'sunny',
                effects: {
                    sunny: { incomeMultiplier: 1.2, description: 'Perfect weather for business!' },
                    rainy: { incomeMultiplier: 0.8, description: 'Rain is keeping customers away...' },
                    stormy: { incomeMultiplier: 0.5, description: 'Severe weather warning!' },
                    snowy: { incomeMultiplier: 0.9, description: 'Snow is slowing things down...' }
                }
            },
            miniGames: {
                available: [
                    {
                        id: 'krusty_quiz',
                        name: 'Krusty\'s Quiz',
                        description: 'Test your Springfield knowledge!',
                        reward: { money: 200, donuts: 5 },
                        cooldown: 3600 // 1 hour
                    },
                    {
                        id: 'duff_racing',
                        name: 'Duff Racing',
                        description: 'Race against other Springfield residents!',
                        reward: { money: 300, donuts: 8 },
                        cooldown: 7200 // 2 hours
                    }
                ],
                lastPlayed: {}
            },
            trading: {
                items: [
                    { id: 'duff_beer', name: 'Duff Beer', basePrice: 50, fluctuation: 0.2 },
                    { id: 'krusty_burger', name: 'Krusty Burger', basePrice: 30, fluctuation: 0.15 },
                    { id: 'lard_lad', name: 'Lard Lad Donut', basePrice: 20, fluctuation: 0.1 }
                ],
                inventory: {},
                lastUpdate: null
            },
            research: {
                points: 0,
                completed: [],
                available: [
                    {
                        id: 'advanced_construction',
                        name: 'Advanced Construction',
                        description: 'Buildings generate 25% more income',
                        cost: 1000,
                        duration: 3600, // 1 hour
                        effect: (state) => {
                            state.buildings.forEach(building => {
                                building.income *= 1.25;
                            });
                        }
                    },
                    {
                        id: 'automated_factories',
                        name: 'Automated Factories',
                        description: 'Factories generate income 50% faster',
                        cost: 2000,
                        duration: 7200, // 2 hours
                        effect: (state) => {
                            state.buildings.forEach(building => {
                                if (building.type === 'factory') {
                                    building.interval /= 1.5;
                                }
                            });
                        }
                    },
                    {
                        id: 'efficient_stores',
                        name: 'Efficient Stores',
                        description: 'Stores require less maintenance',
                        cost: 1500,
                        duration: 5400, // 1.5 hours
                        effect: (state) => {
                            state.buildings.forEach(building => {
                                if (building.type === 'store') {
                                    building.income *= 1.5;
                                }
                            });
                        }
                    }
                ],
                active: []
            },
            buildings: [],
            characters: [
                {
                    name: 'Homer',
                    status: 'idle',
                    task: null,
                    level: 1,
                    xp: 0,
                    xpToNextLevel: 50,
                    upgrades: {
                        speed: 1,
                        efficiency: 1
                    }
                },
                {
                    name: 'Marge',
                    status: 'idle',
                    task: null,
                    level: 1,
                    xp: 0,
                    xpToNextLevel: 50,
                    upgrades: {
                        speed: 1,
                        efficiency: 1
                    }
                }
            ],
            tasks: [
                { name: 'Work at Factory', duration: 60, reward: 100, xp: 10 },
                { name: 'Work at Store', duration: 30, reward: 50, xp: 5 },
                { name: 'Clean House', duration: 15, reward: 25, xp: 3 }
            ],
            specialEvents: [
                {
                    id: 'springfield_fair',
                    name: 'Springfield Fair',
                    description: 'A special event where all buildings generate 2x income!',
                    duration: 3600,
                    active: false,
                    startTime: null,
                    effect: (state) => {
                        state.buildings.forEach(building => {
                            building.income *= 2;
                        });
                    },
                    reset: (state) => {
                        state.buildings.forEach(building => {
                            building.income /= 2;
                        });
                    }
                },
                {
                    id: 'power_plant_outage',
                    name: 'Power Plant Outage',
                    description: 'Factories are not working!',
                    duration: 1800,
                    active: false,
                    startTime: null,
                    effect: (state) => {
                        state.buildings.forEach(building => {
                            if (building.type === 'factory') {
                                building.income = 0;
                            }
                        });
                    },
                    reset: (state) => {
                        state.buildings.forEach(building => {
                            if (building.type === 'factory') {
                                building.income = BUILDINGS.factory.income;
                            }
                        });
                    }
                },
                {
                    id: 'monorail_construction',
                    name: 'Monorail Construction',
                    description: 'A new monorail is being built! All buildings generate 1.5x income!',
                    duration: 5400,
                    active: false,
                    startTime: null,
                    effect: (state) => {
                        state.buildings.forEach(building => {
                            building.income *= 1.5;
                        });
                    },
                    reset: (state) => {
                        state.buildings.forEach(building => {
                            building.income /= 1.5;
                        });
                    }
                }
            ],
            quests: [
                {
                    id: 'first_building',
                    name: 'First Building',
                    description: 'Place your first building',
                    reward: { money: 500, donuts: 5 },
                    completed: false,
                    condition: (state) => state.buildings.length > 0
                },
                {
                    id: 'first_task',
                    name: 'First Task',
                    description: 'Assign a task to a character',
                    reward: { money: 200, donuts: 2 },
                    completed: false,
                    condition: (state) => state.characters.some(c => c.task !== null)
                },
                {
                    id: 'money_maker',
                    name: 'Money Maker',
                    description: 'Earn $5000',
                    reward: { money: 1000, donuts: 10 },
                    completed: false,
                    condition: (state) => state.money >= 5000
                }
            ],
            achievements: [
                {
                    id: 'town_builder',
                    name: 'Town Builder',
                    description: 'Build 5 buildings',
                    reward: { donuts: 10 },
                    completed: false,
                    progress: 0,
                    target: 5
                },
                {
                    id: 'task_master',
                    name: 'Task Master',
                    description: 'Complete 10 tasks',
                    reward: { donuts: 15 },
                    completed: false,
                    progress: 0,
                    target: 10
                }
            ],
            shop: {
                items: [
                    { id: 'speed_boost', name: 'Speed Boost', cost: 50, effect: '2x speed for 1 hour', type: 'boost' },
                    { id: 'money_boost', name: 'Money Boost', cost: 100, effect: '2x money for 1 hour', type: 'boost' },
                    { id: 'new_character', name: 'Bart', cost: 500, effect: 'New character', type: 'character' }
                ],
                activeBoosts: []
            }
        };

        // Building costs and properties
        const BUILDINGS = {
            house: { cost: 500, income: 10, interval: 60 },
            store: { cost: 1000, income: 25, interval: 30 },
            factory: { cost: 2000, income: 50, interval: 15 }
        };

        // API endpoints
        const API = {
            register: '/api/register',
            login: '/api/login',
            save: '/api/save',
            load: '/api/load'
        };

        function showRegister() {
            document.getElementById('loginForm').style.display = 'none';
            document.getElementById('registerForm').style.display = 'block';
        }

        function showLogin() {
            document.getElementById('registerForm').style.display = 'none';
            document.getElementById('loginForm').style.display = 'block';
        }

        // Register new user
        async function register(username, password) {
            try {
                const response = await fetch(API.register, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });

                const data = await response.json();
                if (data.success) {
                    showNotification('Registration successful!', 'success');
                    currentUser = { id: data.userId, username };
                    document.getElementById('registerForm').style.display = 'none';
                    document.getElementById('gameArea').style.display = 'block';
                    updateUI();
                } else {
                    showNotification(data.message, 'error');
                }
            } catch (error) {
                showNotification('Error during registration', 'error');
                console.error('Registration error:', error);
            }
        }

        // Login user
        async function login(username, password) {
            try {
                const response = await fetch(API.login, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });

                const data = await response.json();
                if (data.success) {
                    showNotification('Login successful!', 'success');
                    currentUser = { id: data.userId, username };
                    gameState.money = data.money || 100;
                    gameState.donuts = data.donuts || 0;
                    gameState.level = data.level || 1;
                    gameState.experience = data.experience || 0;
                    document.getElementById('loginForm').style.display = 'none';
                    document.getElementById('gameArea').style.display = 'block';
                    updateUI();
                } else {
                    showNotification(data.message || 'Login failed', 'error');
                }
            } catch (error) {
                showNotification('Error during login', 'error');
                console.error('Login error:', error);
            }
        }

        // Save game state
        async function saveGame() {
            if (!currentUser) return;

            try {
                const response = await fetch(API.save, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        user_id: currentUser.id,
                        game_state: gameState
                    })
                });

                const data = await response.json();
                if (data.success) {
                    showNotification('Game saved successfully!', 'success');
                    } else {
                    showNotification(data.message, 'error');
                }
            } catch (error) {
                showNotification('Error saving game', 'error');
                console.error('Save error:', error);
            }
        }

        // Load game state
        async function loadGame() {
            if (!currentUser) return;

            try {
                const response = await fetch(API.load, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        user_id: currentUser.id
                    })
                });

                const data = await response.json();
                if (data.success) {
                    gameState = data.game_state;
                    showNotification('Game loaded successfully!', 'success');
                    updateUI();
                } else {
                    showNotification(data.message, 'error');
                }
            } catch (error) {
                showNotification('Error loading game', 'error');
                console.error('Load error:', error);
            }
        }

        // Initialize game
        function initializeGame() {
            if (!currentUser) return;

            try {
                // Load game state
                loadGame();

                // Initialize UI
                updateUI();

                // Setup event listeners
                setupEventListeners();

                // Start auto-save
                setInterval(saveGame, 300000); // Save every 5 minutes

                showNotification('Game initialized successfully!', 'success');
            } catch (error) {
                console.error('Error initializing game:', error);
                showNotification('Error initializing game!', 'error');
            }
        }

        // Update UI
        function updateUI() {
            // Update player stats
            document.getElementById('player-money').textContent = `$${gameState.money}`;
            document.getElementById('player-donuts').textContent = gameState.donuts;
            document.getElementById('player-name').textContent = currentUser ? currentUser.username : 'Player';

            // Update buildings
                updateBuildings();

            // Update characters
                updateCharacters();

            // Update inventory
            updateInventory();
        }

        // Setup event listeners
        function setupEventListeners() {
            // Login form
            document.getElementById('login-form').addEventListener('submit', async (e) => {
                e.preventDefault();
                const username = document.getElementById('login-username').value;
                const password = document.getElementById('login-password').value;
                await login(username, password);
            });

            // Register form
            document.getElementById('register-form').addEventListener('submit', async (e) => {
                e.preventDefault();
                const username = document.getElementById('regUsername').value;
                const password = document.getElementById('regPassword').value;
                await register(username, password);
            });

            // Save button
            document.querySelector('.save-btn').addEventListener('click', saveGame);

            // Load button
            document.querySelector('.load-btn').addEventListener('click', loadGame);

            // Logout button
            document.querySelector('.logout-btn').addEventListener('click', () => {
                currentUser = null;
                gameState = {
                    money: 1000,
                    donuts: 10,
                    buildings: [],
                    characters: [],
                    inventory: []
                };
                window.location.reload();
            });
        }

        // Show notification
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        function updateBuildings() {
            gameState.buildings.forEach(building => {
                if (building.lastCollection + building.interval * 1000 <= Date.now()) {
                    gameState.money += building.income;
                    building.lastCollection = Date.now();
                }
            });
        }

        function updateCharacters() {
            gameState.characters.forEach(character => {
                if (character.task && character.task.endTime <= Date.now()) {
                    // Calculate rewards with character efficiency
                    const baseReward = character.task.reward;
                    const efficiencyBonus = character.upgrades.efficiency;
                    const finalReward = baseReward * efficiencyBonus;

                    gameState.money += finalReward;
                    character.xp += character.task.xp;

                    // Check for character level up
                    if (character.xp >= character.xpToNextLevel) {
                        character.level++;
                        character.xp = 0;
                        character.xpToNextLevel = Math.floor(character.xpToNextLevel * 1.5);
                        showNotification(`${character.name} reached level ${character.level}!`);
                    }

                    character.status = 'idle';
                    character.task = null;
                    updateCharacterList();
                }
            });
        }

        function updateInventory() {
            // Implementation of updateInventory function
        }

        function updateCharacterList() {
            const charactersList = document.getElementById('charactersList');
            charactersList.innerHTML = '';

            gameState.characters.forEach(character => {
                const characterDiv = document.createElement('div');
                characterDiv.className = 'character-item';

                const statusText = character.task
                    ? `Working on ${character.task.name} (${Math.ceil((character.task.endTime - Date.now()) / 1000)}s)`
                    : 'Idle';

                characterDiv.innerHTML = `
                    <div class="character-header">
                        <span class="character-name">${character.name}</span>
                        <span class="character-level">Level ${character.level}</span>
                    </div>
                    <div class="character-status">${statusText}</div>
                    <div class="character-xp">
                        <div class="progress-bar">
                            <div class="progress" style="width: ${(character.xp / character.xpToNextLevel) * 100}%"></div>
                        </div>
                        <span class="progress-text">${character.xp}/${character.xpToNextLevel} XP</span>
                    </div>
                    <div class="character-upgrades">
                        <div class="upgrade-item">
                            <span>Speed: Level ${character.upgrades.speed}</span>
                            <button onclick="upgradeCharacter('${character.name}', 'speed')">
                                Upgrade (${Math.floor(50 * Math.pow(1.5, character.upgrades.speed - 1))} donuts)
                            </button>
                        </div>
                        <div class="upgrade-item">
                            <span>Efficiency: Level ${character.upgrades.efficiency}</span>
                            <button onclick="upgradeCharacter('${character.name}', 'efficiency')">
                                Upgrade (${Math.floor(50 * Math.pow(1.5, character.upgrades.efficiency - 1))} donuts)
                            </button>
                        </div>
                    </div>
                    ${!character.task ? `
                        <div class="task-options">
                            ${gameState.tasks.map(task =>
                                `<button onclick="assignTask('${character.name}', '${task.name}')">${task.name}</button>`
                            ).join('')}
                        </div>
                    ` : ''}
                `;
                charactersList.appendChild(characterDiv);
            });
        }

        function assignTask(characterName, taskName) {
            const character = gameState.characters.find(c => c.name === characterName);
            const task = gameState.tasks.find(t => t.name === taskName);

            if (character && task && !character.task) {
                // Calculate duration with character speed
                const baseDuration = task.duration;
                const speedBonus = character.upgrades.speed;
                const finalDuration = baseDuration / speedBonus;

                character.task = {
                    ...task,
                    startTime: Date.now(),
                    endTime: Date.now() + (finalDuration * 1000)
                };
                character.status = 'working';
                updateCharacterList();
                checkQuests();
                checkAchievements();
            }
        }

        function upgradeCharacter(characterName, upgradeType) {
            const character = gameState.characters.find(c => c.name === characterName);
            if (!character) return;

            const upgradeCost = Math.floor(50 * Math.pow(1.5, character.upgrades[upgradeType] - 1));

            if (gameState.donuts >= upgradeCost) {
                gameState.donuts -= upgradeCost;
                character.upgrades[upgradeType]++;
                showNotification(`${character.name}'s ${upgradeType} upgraded to level ${character.upgrades[upgradeType]}!`);
                updateCharacterList();
                updatePlayerStats();
            } else {
                alert('Not enough donuts!');
            }
        }

        function logout() {
            currentUser = null;
            document.getElementById('gameArea').style.display = 'none';
            document.getElementById('loginForm').style.display = 'block';
        }

        function updatePlayerStats() {
            document.getElementById('player-money').textContent = `$${gameState.money}`;
            document.getElementById('player-donuts').textContent = gameState.donuts;
            document.getElementById('researchPoints').textContent = gameState.research.points;
        }

        function checkQuests() {
            gameState.quests.forEach(quest => {
                if (!quest.completed && quest.condition(gameState)) {
                    quest.completed = true;
                    gameState.money += quest.reward.money;
                    gameState.donuts += quest.reward.donuts;
                    showNotification(`Quest Completed: ${quest.name}!`);
                    updateQuestsList();
                }
            });
        }

        function checkAchievements() {
            gameState.achievements.forEach(achievement => {
                if (!achievement.completed) {
                    if (achievement.id === 'town_builder') {
                        achievement.progress = gameState.buildings.length;
                    } else if (achievement.id === 'task_master') {
                        achievement.progress = gameState.characters.reduce((count, char) =>
                            count + (char.task ? 1 : 0), 0);
                    }

                    if (achievement.progress >= achievement.target) {
                        achievement.completed = true;
                        gameState.donuts += achievement.reward.donuts;
                        showNotification(`Achievement Unlocked: ${achievement.name}!`);
                        updateAchievementsList();
                    }
                }
            });
        }

        function updateQuestsList() {
            const questsList = document.getElementById('questsList');
            if (!questsList) return;

            questsList.innerHTML = '';
            gameState.quests.forEach(quest => {
                const questDiv = document.createElement('div');
                questDiv.className = `quest-item ${quest.completed ? 'completed' : ''}`;
                questDiv.innerHTML = `
                    <h4>${quest.name}</h4>
                    <p>${quest.description}</p>
                    <div class="quest-reward">
                        <span>Reward: $${quest.reward.money} + ${quest.reward.donuts} donuts</span>
                    </div>
                    ${quest.completed ? '<span class="completed-badge">Completed!</span>' : ''}
                `;
                questsList.appendChild(questDiv);
            });
        }

        function updateAchievementsList() {
            const achievementsList = document.getElementById('achievementsList');
            if (!achievementsList) return;

            achievementsList.innerHTML = '';
            gameState.achievements.forEach(achievement => {
                const achievementDiv = document.createElement('div');
                achievementDiv.className = `achievement-item ${achievement.completed ? 'completed' : ''}`;
                achievementDiv.innerHTML = `
                    <h4>${achievement.name}</h4>
                    <p>${achievement.description}</p>
                    <div class="progress-bar">
                        <div class="progress" style="width: ${(achievement.progress / achievement.target) * 100}%"></div>
                    </div>
                    <span class="progress-text">${achievement.progress}/${achievement.target}</span>
                    ${achievement.completed ? '<span class="completed-badge">Completed!</span>' : ''}
                `;
                achievementsList.appendChild(achievementDiv);
            });
        }

        function buyShopItem(itemId) {
            const item = gameState.shop.items.find(i => i.id === itemId);
            if (!item) return;

            if (gameState.donuts >= item.cost) {
                gameState.donuts -= item.cost;

                if (item.type === 'boost') {
                    gameState.shop.activeBoosts.push({
                        name: item.name,
                        effect: item.effect,
                        startTime: Date.now(),
                        endTime: Date.now() + 3600000 // 1 hour
                    });
                    showNotification(`${item.name} activated!`);
                } else if (item.type === 'character') {
                    gameState.characters.push({
                        name: item.name,
                        status: 'idle',
                        task: null
                    });
                    showNotification(`Welcome ${item.name} to your town!`);
                    updateCharacterList();
                }

                updatePlayerStats();
            } else {
                alert('Not enough donuts!');
            }
        }

        function initializeShop() {
            const shopItems = document.getElementById('shopItems');
            shopItems.innerHTML = '';

            gameState.shop.items.forEach(item => {
                const itemDiv = document.createElement('div');
                itemDiv.className = 'shop-item';
                itemDiv.innerHTML = `
                    <h4>${item.name}</h4>
                    <p>${item.effect}</p>
                    <div class="shop-item-footer">
                        <span>${item.cost} donuts</span>
                        <button onclick="buyShopItem('${item.id}')">Buy</button>
                    </div>
                `;
                shopItems.appendChild(itemDiv);
            });
        }

        function checkDailyReward() {
            const now = new Date();
            const today = now.toDateString();

            if (gameState.lastDailyReward !== today) {
                const dailyRewardModal = document.createElement('div');
                dailyRewardModal.className = 'modal';
                dailyRewardModal.innerHTML = `
                    <div class="modal-content">
                        <div class="modal-header">
                            <h2>Daily Reward</h2>
                            <button class="close-button" id="closeModalBtn">×</button>
                        </div>
                        <p>Streak: ${gameState.dailyRewardStreak + 1} days</p>
                        <div class="reward-preview">
                            <div class="reward-item">
                                <span>$${100 * (gameState.dailyRewardStreak + 1)}</span>
                            </div>
                            <div class="reward-item">
                                <span>${5 * (gameState.dailyRewardStreak + 1)} donuts</span>
                            </div>
                            <div class="reward-item">
                                <span>${10 * (gameState.dailyRewardStreak + 1)} research points</span>
                            </div>
                        </div>
                        <div class="modal-buttons">
                            <button onclick="claimDailyReward()">Claim Reward</button>
                            <button onclick="closeModal()">Close</button>
                        </div>
                    </div>
                `;
                document.body.appendChild(dailyRewardModal);

                // Add event listener for the close button
                const closeButton = dailyRewardModal.querySelector('#closeModalBtn');
                closeButton.addEventListener('click', closeModal);

                // Add event listener to close modal when clicking outside
                dailyRewardModal.addEventListener('click', (e) => {
                    if (e.target === dailyRewardModal) {
                        closeModal();
                    }
                });
            }
        }

        function closeModal() {
            const modal = document.querySelector('.modal');
            if (modal) {
                modal.remove();
            }
        }

        function claimDailyReward() {
            const now = new Date();
            const today = now.toDateString();

            if (gameState.lastDailyReward === today) return;

            const streakBonus = gameState.dailyRewardStreak + 1;
            gameState.money += 100 * streakBonus;
            gameState.donuts += 5 * streakBonus;
            gameState.research.points += 10 * streakBonus;

            if (gameState.lastDailyReward &&
                new Date(gameState.lastDailyReward).getTime() ===
                new Date(now.setDate(now.getDate() - 1)).getTime()) {
                gameState.dailyRewardStreak++;
            } else {
                gameState.dailyRewardStreak = 0;
            }

            gameState.lastDailyReward = today;
            showNotification(`Daily reward claimed! Streak: ${gameState.dailyRewardStreak}`);
            updatePlayerStats();
            closeModal();
        }

        function updateResearch() {
            const now = Date.now();
            gameState.research.active = gameState.research.active.filter(research => {
                if (research.startTime + (research.duration * 1000) <= now) {
                    research.effect(gameState);
                    showNotification(`Research completed: ${research.name}`);
                    return false;
                }
                return true;
            });
        }

        function startResearch(researchId) {
            const research = gameState.research.available.find(r => r.id === researchId);
            if (!research) return;

            if (gameState.research.points >= research.cost) {
                gameState.research.points -= research.cost;
                gameState.research.active.push({
                    ...research,
                    startTime: Date.now()
                });
                showNotification(`Research started: ${research.name}`);
                updateResearchList();
            } else {
                alert('Not enough research points!');
            }
        }

        function upgradeBuilding(x, y) {
            const building = townGrid[x][y];
            if (!building) return;

            const upgradeCost = Math.floor(building.cost * 1.5);
            if (gameState.money >= upgradeCost) {
                gameState.money -= upgradeCost;
                building.income *= 1.5;
                building.level = (building.level || 1) + 1;
                showNotification(`Building upgraded to level ${building.level}!`);
                updateBuildingDisplay(x, y);
                updatePlayerStats();
            } else {
                alert('Not enough money!');
            }
        }

        function updateBuildingDisplay(x, y) {
            const building = townGrid[x][y];
            if (!building) return;

            const cell = document.querySelector(`.grid-cell[data-x="${x}"][data-y="${y}"]`);
            cell.innerHTML = `
                <div class="building-level">Level ${building.level || 1}</div>
                <div class="building-income">$${building.income}/s</div>
            `;
        }

        function initializeResearchList() {
            const researchList = document.getElementById('researchList');
            researchList.innerHTML = '';

            gameState.research.available.forEach(research => {
                const researchDiv = document.createElement('div');
                researchDiv.className = 'research-item';
                researchDiv.innerHTML = `
                    <h4>${research.name}</h4>
                    <p>${research.description}</p>
                    <div class="research-details">
                        <span>Cost: ${research.cost} research points</span>
                        <span>Duration: ${Math.floor(research.duration / 60)} minutes</span>
                    </div>
                    <button onclick="startResearch('${research.id}')"
                            ${gameState.research.points < research.cost ? 'disabled' : ''}>
                        Start Research
                    </button>
                `;
                researchList.appendChild(researchDiv);
            });
        }

        function handleCellClick(x, y) {
            if (selectedBuilding) {
                placeBuildingAt(x, y);
            } else if (movingBuilding) {
                moveBuildingTo(x, y);
            }
        }

        function placeBuilding(type) {
            if (gameState.money >= BUILDINGS[type].cost) {
                selectedBuilding = type;
                // Add click handlers to all district content areas
                const districts = [
                    'residentialDistrict',
                    'commercialDistrict',
                    'industrialDistrict',
                    'entertainmentDistrict',
                    'educationDistrict',
                    'natureDistrict'
                ];

                districts.forEach(district => {
                    const districtElement = document.getElementById(district);
                    districtElement.onclick = (e) => {
                        const rect = districtElement.getBoundingClientRect();
                        const x = e.clientX - rect.left;
                        const y = e.clientY - rect.top;
                        placeBuildingAt(x, y, district);
                    };
                });

                showNotification(`Selected ${type}. Click in a district to place it.`);
            } else {
                alert('Not enough money!');
            }
        }

        function placeBuildingAt(x, y, district) {
            if (!selectedBuilding) return;

            const building = BUILDINGS[selectedBuilding];
            if (gameState.money >= building.cost) {
                gameState.money -= building.cost;

                const buildingElement = document.createElement('div');
                buildingElement.className = 'building';
                buildingElement.style.left = x + 'px';
                buildingElement.style.top = y + 'px';
                buildingElement.innerHTML = `
                    <div class="building-level">Level 1</div>
                            <div class="building-income">$${building.income}/s</div>
                    <div class="building-name">${building.name}</div>
                `;

                // Add drag functionality
                buildingElement.draggable = true;
                buildingElement.addEventListener('dragstart', (e) => {
                    e.dataTransfer.setData('text/plain', buildingElement.id);
                    buildingElement.classList.add('moving');
                });

                buildingElement.addEventListener('dragend', (e) => {
                    buildingElement.classList.remove('moving');
                    const rect = document.getElementById(district).getBoundingClientRect();
                    const x = e.clientX - rect.left;
                    const y = e.clientY - rect.top;
                    building.x = x;
                    building.y = y;
                    buildingElement.style.left = x + 'px';
                    buildingElement.style.top = y + 'px';
                });

                document.getElementById(district).appendChild(buildingElement);

                gameState.buildings.push({
                    type: selectedBuilding,
                    x: x,
                    y: y,
                    district: district,
                    lastCollection: Date.now(),
                    ...building
                });

                // Remove click handlers after placement
                const districts = [
                    'residentialDistrict',
                    'commercialDistrict',
                    'industrialDistrict',
                    'entertainmentDistrict',
                    'educationDistrict',
                    'natureDistrict'
                ];

                districts.forEach(district => {
                    const districtElement = document.getElementById(district);
                    districtElement.onclick = null;
                });

                selectedBuilding = null;
                updatePlayerStats();
                checkQuests();
                checkAchievements();
            } else {
                alert('Not enough money!');
            }
        }

        function startMovingBuilding(x, y) {
            const building = townGrid[x][y];
            if (building) {
                movingBuilding = { x, y, building };
                const cell = document.querySelector(`.grid-cell[data-x="${x}"][data-y="${y}"]`);
                cell.classList.add('building-moving');
                showNotification('Click on a new location to move the building');
            }
        }

        function moveBuildingTo(newX, newY) {
            if (!movingBuilding) return;

            const { x: oldX, y: oldY, building } = movingBuilding;

            if (townGrid[newX][newY] === null) {
                // Clear old position
                townGrid[oldX][oldY] = null;
                const oldCell = document.querySelector(`.grid-cell[data-x="${oldX}"][data-y="${oldY}"]`);
                oldCell.className = 'grid-cell';

                // Place in new position
                townGrid[newX][newY] = building;
                const newCell = document.querySelector(`.grid-cell[data-x="${newX}"][data-y="${newY}"]`);
                newCell.className = `grid-cell building-${building.type}`;
                newCell.innerHTML = `
                    <div class="building-level">Level ${building.level || 1}</div>
                    <div class="building-income">$${building.income}/s</div>
                `;

                showNotification('Building moved successfully!');
            } else {
                showNotification('Cannot move building to an occupied space!');
            }

            // Reset moving state
            movingBuilding = null;
            const cells = document.querySelectorAll('.building-moving');
            cells.forEach(cell => cell.classList.remove('building-moving'));
        }

        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            setupEventListeners();
        });
    </script>
</body>
</html>