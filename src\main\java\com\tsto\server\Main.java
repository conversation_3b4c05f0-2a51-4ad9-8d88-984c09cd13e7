package com.tsto.server;

import java.util.logging.Logger;
import java.util.logging.Level;

public class Main {
    private static final Logger LOGGER = Logger.getLogger(Main.class.getName());
    private static TSTOServer server;
    private static GameManager gameManager;

    public static void main(String[] args) {
        try {
            // Configure logging
            configureLogging();
            
            LOGGER.info("Starting server...");

            // Load server configuration
            ServerConfig config = new ServerConfig();
            
            // Create and initialize server with config
            server = new TSTOServer(config);
            gameManager = GameManager.getInstance();
            
            // Default port 8080, can be changed via command line args
            int port = config.getPort();
            if (args.length > 0) {
                try {
                    port = Integer.parseInt(args[0]);
                    config.setPort(port);
                } catch (NumberFormatException e) {
                    LOGGER.warning("Invalid port number provided. Using default port " + port);
                }
            }

            // Initialize and start server
            if (server.initialize(port)) {
                server.start();
                
                // Add shutdown hook
                Runtime.getRuntime().addShutdownHook(new Thread(() -> {
                    LOGGER.info("Server shutting down...");
                    shutdown();
                }));
            } else {
                LOGGER.severe("Failed to initialize server");
                System.exit(1);
            }
        } catch (Exception e) {
            LOGGER.severe("Fatal error during server startup: " + e.getMessage());
            e.printStackTrace();
            System.exit(1);
        }
    }

    private static void shutdown() {
        try {
            if (server != null) {
                server.stop();
            }
            if (gameManager != null) {
                gameManager.shutdown();
            }
            LOGGER.info("Server shutdown completed");
        } catch (Exception e) {
            LOGGER.severe("Error during shutdown: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static void configureLogging() {
        // Configure logging format and level
        System.setProperty("java.util.logging.SimpleFormatter.format",
                "[%1$tF %1$tT] [%4$-7s] %5$s %n");
        Logger rootLogger = Logger.getLogger("");
        rootLogger.setLevel(Level.INFO);
    }
} 