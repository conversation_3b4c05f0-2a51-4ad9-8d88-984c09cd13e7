:root {
    --simpsons-yellow: #FFD90F;
    --simpsons-blue: #1D75B9;
    --simpsons-red: #E62E2D;
    --simpsons-brown: #8B4513;
    --simpsons-pink: #FF69B4;
    --simpsons-orange: #FFA500;
    --simpsons-green: #4CAF50;
    --simpsons-purple: #800080;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Comic Sans MS', cursive, sans-serif;
    background-color: var(--simpsons-yellow);
    color: #000;
    margin: 0;
    padding: 0;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background-color: white;
    border: 5px solid var(--simpsons-blue);
    border-radius: 15px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
}

h1, h2, h3 {
    color: var(--simpsons-blue);
    text-shadow: 2px 2px 0 rgba(0, 0, 0, 0.1);
    font-family: 'Impact', sans-serif;
}

.game-header {
    background-color: var(--simpsons-blue);
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.player-info {
    display: flex;
    gap: 20px;
}

.player-info span {
    background-color: var(--simpsons-yellow);
    padding: 8px 15px;
    border-radius: 20px;
    font-weight: bold;
    border: 2px solid var(--simpsons-blue);
}

.game-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 20px;
}

.town-container {
    display: flex;
    gap: 20px;
    height: calc(100vh - 100px);
    padding: 20px;
}

.town-sidebar {
    width: 300px;
    background-color: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.town-main {
    flex: 1;
    background-color: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: auto;
}

.town-canvas {
    width: 100%;
    height: 100%;
    background-color: #8BC34A;
    border-radius: 8px;
    overflow: auto;
}

.town-grid {
    display: grid;
    gap: 1px;
    background-color: #689F38;
    padding: 1px;
    border-radius: 4px;
    overflow: hidden;
}

.grid-cell {
    background-color: #8BC34A;
    border: 1px solid #689F38;
    position: relative;
    transition: background-color 0.2s;
}

.grid-cell:hover {
    background-color: #7CB342;
}

.grid-cell.occupied {
    background-color: #689F38;
}

.grid-cell.building-moving {
    background-color: #FFC107;
    opacity: 0.7;
}

.building-categories {
    background-color: white;
    border-radius: 8px;
    padding: 15px;
}

.category-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 10px;
}

.category-item {
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
}

.category-item:hover {
    background-color: #e9ecef;
}

.category-item.active {
    background-color: #007bff;
    color: white;
}

.building-list {
    background-color: white;
    border-radius: 8px;
    padding: 15px;
    flex: 1;
    overflow-y: auto;
}

.buildings-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-top: 10px;
}

.building-item {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 10px;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    gap: 10px;
    align-items: center;
}

.building-item:hover {
    background-color: #e9ecef;
    transform: translateY(-2px);
}

.building-preview {
    background-color: #CDDC39;
    border-radius: 4px;
    border: 2px solid #8BC34A;
}

.building-info {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.building-name {
    font-weight: bold;
    color: #2c3e50;
}

.building-cost {
    color: #e74c3c;
    font-size: 0.9em;
}

.building-income {
    color: #27ae60;
    font-size: 0.9em;
}

.building {
    position: relative;
    background-color: #CDDC39;
    border: 2px solid #8BC34A;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
    overflow: hidden;
    user-select: none;
}

.building:hover {
    transform: scale(1.05);
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
}

.building.moving {
    opacity: 0.7;
    border: 2px dashed #FFC107;
}

.building-level {
    position: absolute;
    top: 4px;
    left: 4px;
    background-color: rgba(0, 0, 0, 0.7);
    color: #fff;
    padding: 2px 4px;
    border-radius: 4px;
    font-size: 12px;
}

.building-income {
    position: absolute;
    bottom: 4px;
    left: 4px;
    background-color: rgba(0, 0, 0, 0.7);
    color: #fff;
    padding: 2px 4px;
    border-radius: 4px;
    font-size: 12px;
}

.building-name {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(0, 0, 0, 0.7);
    color: #fff;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 14px;
    text-align: center;
    width: 90%;
}

.building-actions {
    position: absolute;
    bottom: 4px;
    right: 4px;
    display: flex;
    gap: 4px;
    z-index: 2;
}

.upgrade-btn {
    background-color: #4CAF50;
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.upgrade-btn:hover {
    background-color: #45a049;
}

.upgrade-btn:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
}

.context-menu {
    position: fixed;
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    min-width: 150px;
    overflow: hidden;
}

.context-menu-item {
    padding: 8px 12px;
    cursor: pointer;
    transition: background-color 0.2s;
    display: flex;
    align-items: center;
    gap: 8px;
}

.context-menu-item:hover {
    background-color: #f5f5f5;
}

.context-menu-item.danger {
    color: #f44336;
}

.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 24px;
    border-radius: 4px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    animation: slideIn 0.3s ease-out;
    display: flex;
    align-items: center;
    gap: 8px;
}

.notification.success {
    background-color: #4CAF50;
    color: white;
}

.notification.error {
    background-color: #f44336;
    color: white;
}

.notification.warning {
    background-color: #FFC107;
    color: #000;
}

.notification.info {
    background-color: #2196F3;
    color: white;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.income-popup {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.7);
    color: #fff;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: bold;
    pointer-events: none;
    z-index: 100;
    animation: incomePopup 1s ease-out forwards;
}

@keyframes incomePopup {
    0% {
        opacity: 0;
        transform: translate(-50%, 0);
    }
    20% {
        opacity: 1;
    }
    80% {
        opacity: 1;
    }
    100% {
        opacity: 0;
        transform: translate(-50%, -50px);
    }
}

.game-menu {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.menu-section {
    background-color: white;
    border: 3px solid var(--simpsons-blue);
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.menu-section h3 {
    margin-top: 0;
    color: #2c3e50;
    border-bottom: 2px solid #eee;
    padding-bottom: 10px;
}

.building-item, .character-item, .task-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.building-item:hover, .character-item:hover {
    background-color: #e9ecef;
}

button {
    background-color: var(--simpsons-blue);
    color: #ffffff !important;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.2s;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

button:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

button:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* Save and Load button specific styles */
.save-btn, .load-btn {
    background-color: var(--simpsons-blue);
    color: #ffffff !important;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    padding: 12px 24px;
    margin: 5px;
    border: 2px solid #ffffff;
}

.save-btn:hover, .load-btn:hover {
    background-color: var(--simpsons-red);
    color: #ffffff !important;
    border-color: #ffffff;
}

.save-btn:disabled, .load-btn:disabled {
    background-color: var(--simpsons-blue);
    opacity: 0.7;
    cursor: wait;
    color: #ffffff !important;
}

.save-btn:disabled::after, .load-btn:disabled::after {
    content: '';
    display: inline-block;
    width: 16px;
    height: 16px;
    margin-left: 8px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
    vertical-align: middle;
}

.logout-btn {
    background-color: #e74c3c;
    color: #ffffff !important;
}

.logout-btn:hover {
    background-color: #c0392b;
    color: #ffffff !important;
}

/* Form styles */
.form-group {
    margin-bottom: 15px;
}

label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: var(--simpsons-blue);
}

input {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-sizing: border-box;
    font-size: 14px;
}

input:focus {
    outline: none;
    border-color: var(--simpsons-blue);
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
}

/* Login/Register forms */
#loginForm, #registerForm {
    max-width: 400px;
    margin: 0 auto;
    padding: 20px;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

#loginForm button, #registerForm button {
    width: 100%;
    margin: 10px 0;
    padding: 12px;
    font-size: 16px;
    color: #ffffff !important;
    background-color: var(--simpsons-blue);
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
    position: relative;
}

#loginForm button:disabled, #registerForm button:disabled {
    background-color: var(--simpsons-blue);
    opacity: 0.8;
    cursor: wait;
}

#loginForm button:disabled span, #registerForm button:disabled span {
    color: #ffffff !important;
    opacity: 1;
}

/* Loading spinner styles */
.loading-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
    margin-right: 8px;
    vertical-align: middle;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.error-message {
    color: #ff0000;
    margin-top: 5px;
    font-size: 0.9em;
}

.success-message {
    color: #4CAF50;
    margin-top: 5px;
    font-size: 0.9em;
}

.section {
    background-color: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

h2 {
    margin-bottom: 20px;
    color: #2c3e50;
}

form {
    display: flex;
    flex-direction: column;
    gap: 15px;
    max-width: 400px;
    margin: 0 auto;
}

.game-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ddd;
}

.player-info {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.game-content {
    display: flex;
    gap: 20px;
    min-height: 500px;
}

.town-section {
    background-color: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.tile {
    aspect-ratio: 1;
    background-color: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
}

/* Side Panel Sections */
.characters-section,
.shop-section,
.quests-section,
.friends-section {
    background-color: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.character-list,
.shop-items,
.quest-list,
.friend-list {
    margin-top: 15px;
}

.character,
.shop-item,
.quest,
.friend {
    padding: 10px;
    margin-bottom: 10px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border-left: 4px solid #3498db;
}

.friend-request {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.friend-request input {
    flex: 1;
    margin-bottom: 0;
}

.friend-request button {
    width: auto;
    padding: 10px 15px;
}

/* Game Elements */
.task-options {
    display: flex;
    gap: 5px;
    margin-top: 5px;
}

.task-options button {
    padding: 4px 8px;
    font-size: 0.8em;
    background-color: #3498db;
}

.task-options button:hover {
    background-color: #2980b9;
}

/* Character Status */
.character-item {
    position: relative;
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 4px;
    margin-bottom: 10px;
}

.character-item.working {
    background-color: #e3f2fd;
}

.character-item .status {
    font-size: 0.9em;
    color: #666;
}

/* Game Stats */
.player-info span {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 8px 15px;
    background-color: #f8f9fa;
    border-radius: 4px;
    font-weight: bold;
}

.player-info span::before {
    content: '';
    display: inline-block;
    width: 16px;
    height: 16px;
    background-size: contain;
    background-repeat: no-repeat;
}

#player-money::before {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path fill="%234CAF50" d="M11.8 10.9c-2.27-.59-3-1.2-3-2.15 0-1.09 1.01-1.85 2.7-1.85 1.78 0 2.44.85 2.5 2.1h2.21c-.07-1.72-1.12-3.3-3.21-3.81V3h-3v2.16c-1.94.42-3.5 1.68-3.5 3.61 0 2.31 1.91 3.46 4.7 4.13 2.5.6 3 1.48 3 2.41 0 .69-.49 1.79-2.7 1.79-2.06 0-2.87-.92-2.98-2.1h-2.2c.12 2.19 1.76 3.42 3.68 3.83V21h3v-2.15c1.95-.37 3.5-1.5 3.5-3.55 0-2.84-2.43-3.81-4.7-4.4z"/></svg>');
}

#player-donuts::before {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path fill="%23FF9800" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z"/></svg>');
}

/* Quests and Achievements */
.quest-item, .achievement-item {
    background-color: var(--simpsons-yellow);
    border: 2px solid var(--simpsons-blue);
    border-radius: 8px;
    padding: 10px;
    margin-bottom: 10px;
    position: relative;
}

.quest-item.completed, .achievement-item.completed {
    background-color: #e8f5e9;
    border-left: 4px solid #4CAF50;
}

.quest-item h4, .achievement-item h4 {
    margin: 0 0 5px 0;
    color: #2c3e50;
}

.quest-reward {
    margin-top: 10px;
    font-size: 0.9em;
    color: #666;
}

.completed-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: #4CAF50;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.8em;
}

/* Progress Bar */
.progress-bar {
    background-color: #ddd;
    border-radius: 10px;
    overflow: hidden;
    border: 2px solid var(--simpsons-blue);
    height: 6px;
    margin: 10px 0;
}

.progress {
    background-color: var(--simpsons-green);
    height: 100%;
    transition: width 0.3s;
}

.progress-text {
    font-size: 0.8em;
    color: #666;
}

/* Shop Styles */
.shop-container {
    display: flex;
    gap: 20px;
    padding: 20px;
}

.shop-sidebar {
    width: 250px;
    background: #f5f5f5;
    border-radius: 8px;
    padding: 15px;
}

.shop-categories h3 {
    margin-bottom: 15px;
    color: #333;
}

.category-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.category-item {
    padding: 10px;
    background: #fff;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.category-item:hover {
    background: #e0e0e0;
}

.category-item.active {
    background: #4CAF50;
    color: white;
}

.shop-main {
    flex: 1;
    background: #f5f5f5;
    border-radius: 8px;
    padding: 20px;
}

.shop-items {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
}

.shop-item {
    background: white;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.shop-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.shop-item-header h3 {
    margin: 0;
    color: #333;
}

.item-cost {
    font-weight: bold;
    color: #4CAF50;
}

.item-description {
    color: #666;
    margin-bottom: 15px;
}

.buy-btn {
    width: 100%;
    padding: 10px;
    background: #4CAF50;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.buy-btn:hover {
    background: #45a049;
}

.buy-btn:disabled {
    background: #cccccc;
    cursor: not-allowed;
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 4px;
    color: white;
    z-index: 1000;
    animation: slideIn 0.3s ease-out;
}

.notification.success {
    background: #4CAF50;
}

.notification.error {
    background: #f44336;
}

.notification.info {
    background: #2196F3;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Active Boosts */
.active-boosts {
    position: fixed;
    top: 20px;
    left: 20px;
    display: flex;
    gap: 10px;
}

.boost-item {
    background-color: rgba(76, 175, 80, 0.9);
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 0.9em;
    display: flex;
    align-items: center;
    gap: 5px;
}

/* Character Upgrades */
.character-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.character-name {
    font-weight: bold;
    font-size: 1.1em;
}

.character-level {
    background-color: #3498db;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.9em;
}

.character-status {
    color: #666;
    margin-bottom: 10px;
}

.character-xp {
    margin-bottom: 10px;
}

.character-upgrades {
    background-color: #f1f8e9;
    padding: 10px;
    border-radius: 4px;
    margin-bottom: 10px;
}

.upgrade-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.upgrade-item:last-child {
    margin-bottom: 0;
}

.upgrade-item button {
    padding: 4px 8px;
    font-size: 0.8em;
    background-color: #8bc34a;
}

.upgrade-item button:hover {
    background-color: #7cb342;
}

/* Special Events */
.active-events {
    position: fixed;
    top: 20px;
    left: 20px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    z-index: 1000;
}

.event-item {
    background-color: var(--simpsons-red);
    color: white;
    border: 3px solid var(--simpsons-yellow);
    padding: 10px 15px;
    border-radius: 4px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    max-width: 300px;
    animation: pulse 2s infinite;
}

.event-item h4 {
    margin: 0 0 5px 0;
    font-size: 1.1em;
}

.event-item p {
    margin: 0;
    font-size: 0.9em;
    opacity: 0.9;
}

.event-timer {
    margin-top: 5px;
    font-size: 0.8em;
    opacity: 0.8;
}

/* Level Up Animation */
@keyframes levelUp {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.5);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.level-up {
    animation: levelUp 0.5s ease;
}

/* Research System */
.research-header {
    background-color: #e3f2fd;
    padding: 10px;
    border-radius: 4px;
    margin-bottom: 10px;
    text-align: center;
    font-weight: bold;
}

.research-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.research-item {
    background-color: #f8f9fa;
    border-radius: 4px;
    padding: 15px;
    border-left: 4px solid #2196F3;
}

.research-item h4 {
    margin: 0 0 5px 0;
    color: #2c3e50;
}

.research-details {
    display: flex;
    justify-content: space-between;
    margin: 10px 0;
    font-size: 0.9em;
    color: #666;
}

.research-item button {
    width: 100%;
    background-color: #2196F3;
}

.research-item button:hover {
    background-color: #1976D2;
}

.research-item button:disabled {
    background-color: #BDBDBD;
    cursor: not-allowed;
}

/* Daily Rewards */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background-color: var(--simpsons-yellow);
    border: 5px solid var(--simpsons-blue);
    border-radius: 15px;
    padding: 20px;
    max-width: 400px;
    margin: 100px auto;
    position: relative;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.modal-header h2 {
    margin: 0;
    color: #2c3e50;
}

.close-button {
    position: absolute;
    top: 10px;
    right: 10px;
    background: var(--simpsons-red);
    color: white;
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    font-size: 20px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-button:hover {
    color: #333;
}

.reward-info {
    margin: 20px 0;
    padding: 10px;
    background-color: #f0f0f0;
    border-radius: 5px;
}

.modal-buttons {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 20px;
}

.modal-buttons button {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s;
}

.modal-buttons button:first-child {
    background-color: #4CAF50;
    color: white;
}

.modal-buttons button:first-child:hover {
    background-color: #45a049;
}

.modal-buttons button:last-child {
    background-color: #f44336;
    color: white;
}

.modal-buttons button:last-child:hover {
    background-color: #da190b;
}

.reward-popup {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: #fff;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
    z-index: 1001;
    animation: popup 0.3s ease-out;
}

.reward-content {
    text-align: center;
}

.reward-content h3 {
    color: #4CAF50;
    margin-bottom: 10px;
}

.reward-content p {
    margin: 5px 0;
    font-size: 18px;
}

.reward-content button {
    margin-top: 15px;
    padding: 8px 16px;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
}

@keyframes popup {
    from {
        transform: translate(-50%, -50%) scale(0.8);
        opacity: 0;
    }
    to {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
    }
}

/* Active Research */
.active-research {
    position: fixed;
    bottom: 20px;
    right: 20px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    z-index: 1000;
}

.active-research-item {
    background-color: rgba(33, 150, 243, 0.9);
    color: white;
    padding: 10px 15px;
    border-radius: 4px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

.active-research-item h4 {
    margin: 0 0 5px 0;
    font-size: 1.1em;
}

.research-progress {
    height: 4px;
    background-color: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
    margin-top: 5px;
    overflow: hidden;
}

.research-progress-bar {
    height: 100%;
    background-color: white;
    transition: width 0.3s ease;
}

/* Weather System */
.weather-display {
    background-color: var(--simpsons-blue);
    color: white;
    padding: 10px 20px;
    border-radius: 20px;
    border: 3px solid var(--simpsons-yellow);
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
}

.weather-sunny {
    color: #ffd700;
}

.weather-rainy {
    color: #1e90ff;
}

.weather-stormy {
    color: #ff4500;
}

.weather-snowy {
    color: #ffffff;
}

/* Mini-Games */
.mini-game-item {
    background-color: var(--simpsons-purple);
    color: white;
    border: 3px solid var(--simpsons-yellow);
    padding: 15px;
    margin: 10px 0;
    border-radius: 5px;
    transition: transform 0.2s;
}

.mini-game-item:hover {
    transform: translateY(-2px);
}

.mini-game-item h4 {
    color: #3498db;
    margin: 0 0 10px 0;
}

.mini-game-item p {
    margin: 5px 0;
    color: #ecf0f1;
}

.mini-game-item button {
    background: #27ae60;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 3px;
    cursor: pointer;
    margin-top: 10px;
    transition: background 0.2s;
}

.mini-game-item button:hover {
    background: #219a52;
}

.mini-game-item button:disabled {
    background: #7f8c8d;
    cursor: not-allowed;
}

/* Trading System */
.trading-item {
    background-color: var(--simpsons-orange);
    border: 3px solid var(--simpsons-blue);
    padding: 15px;
    margin: 10px 0;
    border-radius: 5px;
    transition: transform 0.2s;
}

.trading-item:hover {
    transform: translateY(-2px);
}

.trading-item h4 {
    color: #e74c3c;
    margin: 0 0 10px 0;
}

.trading-item p {
    margin: 5px 0;
    color: #ecf0f1;
}

.trading-buttons {
    display: flex;
    gap: 10px;
    margin-top: 10px;
}

.trading-buttons button {
    flex: 1;
    padding: 8px;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    transition: background 0.2s;
}

.trading-buttons button:first-child {
    background: #27ae60;
    color: white;
}

.trading-buttons button:first-child:hover {
    background: #219a52;
}

.trading-buttons button:last-child {
    background: #e74c3c;
    color: white;
}

.trading-buttons button:last-child:hover {
    background: #c0392b;
}

.trading-buttons button:disabled {
    background: #7f8c8d;
    cursor: not-allowed;
}

/* Special Events */
.event-item {
    background-color: var(--simpsons-red);
    color: white;
    border: 3px solid var(--simpsons-yellow);
    padding: 15px;
    margin: 10px 0;
    border-radius: 5px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(255, 0, 0, 0.4);
    }
    70% {
        transform: scale(1.05);
        box-shadow: 0 0 0 10px rgba(255, 0, 0, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(255, 0, 0, 0);
    }
}

.event-item h4 {
    color: #f1c40f;
    margin: 0 0 10px 0;
}

.event-item p {
    margin: 5px 0;
    color: #ecf0f1;
}

/* Responsive Design */
@media (max-width: 768px) {
    .game-content {
        grid-template-columns: 1fr;
    }

    .player-info {
        flex-direction: column;
        gap: 10px;
    }

    .town-canvas {
        min-height: 300px;
    }

    .task-options {
        flex-direction: column;
    }

    .notification {
        left: 20px;
        right: 20px;
        text-align: center;
    }

    .active-boosts {
        top: auto;
        bottom: 20px;
        flex-wrap: wrap;
        justify-content: center;
    }

    .character-upgrades {
        flex-direction: column;
    }

    .upgrade-item {
        flex-direction: column;
        gap: 5px;
    }

    .upgrade-item button {
        width: 100%;
    }

    .active-events {
        top: auto;
        bottom: 20px;
        left: 20px;
        right: 20px;
    }

    .event-item {
        max-width: none;
    }

    .research-details {
        flex-direction: column;
        gap: 5px;
    }

    .reward-preview {
        flex-direction: column;
        gap: 10px;
    }

    .reward-item {
        width: 100%;
    }

    .active-research {
        left: 20px;
        right: 20px;
    }

    .weather-display {
        top: auto;
        bottom: 20px;
        left: 20px;
        right: auto;
    }

    .mini-game-item,
    .trading-item {
        padding: 10px;
    }

    .trading-buttons {
        flex-direction: column;
    }

    .container {
        padding: 10px;
    }
    
    .game-header {
        flex-direction: column;
        gap: 10px;
    }
    
    .player-info {
        flex-direction: column;
        gap: 10px;
    }
}

/* Town header */
.town-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background-color: var(--simpsons-blue);
    border-radius: 10px;
    color: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.town-header h2 {
    margin: 0;
    font-size: 24px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.town-stats {
    display: flex;
    gap: 15px;
}

.town-stat {
    background-color: rgba(255, 255, 255, 0.2);
    padding: 8px 15px;
    border-radius: 20px;
    font-weight: bold;
    display: flex;
    align-items: center;
    gap: 5px;
}

/* Town controls */
.town-controls {
    display: flex;
    gap: 10px;
    margin-top: 20px;
    padding: 15px;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.town-controls button {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    background-color: var(--simpsons-blue);
    color: white;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.town-controls button:hover {
    background-color: var(--simpsons-red);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Building selection menu */
.building-menu {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background-color: white;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 100;
}

.building-menu.active {
    display: block;
}

.building-options {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
    margin-top: 15px;
}

.building-option {
    padding: 15px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.building-option:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Responsive design */
@media (max-width: 768px) {
    .town-canvas {
        gap: 2px;
        padding: 10px;
    }
    
    .town-header {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
    
    .town-stats {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .town-controls {
        flex-wrap: wrap;
        justify-content: center;
    }
}

.navigation-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 10px;
}

.nav-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 4px;
    text-decoration: none;
    color: #2c3e50;
    transition: all 0.3s ease;
}

.nav-item:hover {
    background-color: #e9ecef;
    transform: translateX(5px);
}

.back-btn {
    background-color: #6c757d;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.back-btn:hover {
    background-color: #5a6268;
}

/* Error states */
.error {
    background-color: #ffebee;
    border-color: #f44336;
}

.error-text {
    color: #f44336;
    font-size: 12px;
    margin-top: 4px;
}

/* Loading states */
.loading {
    position: relative;
    opacity: 0.7;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Building states */
.building.placing {
    opacity: 0.7;
    border: 2px dashed #FFC107;
}

.building.error {
    border-color: #f44336;
    animation: shake 0.5s;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* Grid improvements */
.town-grid {
    display: grid;
    gap: 1px;
    background-color: #689F38;
    padding: 1px;
    border-radius: 4px;
    overflow: hidden;
}

.grid-cell {
    background-color: #8BC34A;
    border: 1px solid #689F38;
    position: relative;
    transition: background-color 0.2s;
}

.grid-cell:hover {
    background-color: #7CB342;
}

.grid-cell.occupied {
    background-color: #689F38;
}

.grid-cell.building-moving {
    background-color: #FFC107;
    opacity: 0.7;
}

/* Building improvements */
.building {
    position: relative;
    background-color: #CDDC39;
    border: 2px solid #8BC34A;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
    overflow: hidden;
    user-select: none;
}

.building:hover {
    transform: scale(1.05);
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
}

.building.moving {
    opacity: 0.7;
    border: 2px dashed #FFC107;
}

.building-actions {
    position: absolute;
    bottom: 4px;
    right: 4px;
    display: flex;
    gap: 4px;
    z-index: 2;
}

/* Button improvements */
button {
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
    font-weight: bold;
}

button:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

button:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* Context menu improvements */
.context-menu {
    position: fixed;
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    min-width: 150px;
    overflow: hidden;
}

.context-menu-item {
    padding: 8px 12px;
    cursor: pointer;
    transition: background-color 0.2s;
    display: flex;
    align-items: center;
    gap: 8px;
}

.context-menu-item:hover {
    background-color: #f5f5f5;
}

.context-menu-item.danger {
    color: #f44336;
}

/* Responsive improvements */
@media (max-width: 768px) {
    .town-grid {
        gap: 2px;
        padding: 2px;
    }
    
    .grid-cell {
        min-width: 40px;
        min-height: 40px;
    }
    
    .building {
        font-size: 12px;
    }
    
    .building-actions {
        flex-direction: column;
    }
    
    .notification {
        left: 20px;
        right: 20px;
        top: auto;
        bottom: 20px;
    }
} 