/**
 * WebGL Sprites and Animations for TSTO Web Game
 * Provides sprite and animation management for WebGL rendering
 */

class SpriteManager {
    /**
     * Initialize the sprite manager
     * @param {WebGLRenderer} renderer - The WebGL renderer
     */
    constructor(renderer) {
        this.renderer = renderer;
        this.sprites = {};
        this.animations = {};
        this.animationFrames = {};
    }
    
    /**
     * Create a sprite
     * @param {string} id - The ID of the sprite
     * @param {string} textureName - The name of the texture
     * @param {number} x - The x position
     * @param {number} y - The y position
     * @param {number} width - The width
     * @param {number} height - The height
     * @param {Object} options - Additional options
     * @returns {Object} - The sprite object
     */
    createSprite(id, textureName, x, y, width, height, options = {}) {
        const sprite = this.renderer.createSprite(textureName, x, y, width, height, options);
        if (sprite) {
            this.sprites[id] = sprite;
        }
        return sprite;
    }
    
    /**
     * Create a sprite from an atlas
     * @param {string} id - The ID of the sprite
     * @param {TextureAtlas} atlas - The texture atlas
     * @param {string} regionName - The name of the region
     * @param {number} x - The x position
     * @param {number} y - The y position
     * @param {Object} options - Additional options
     * @returns {Object} - The sprite object
     */
    createSpriteFromAtlas(id, atlas, regionName, x, y, options = {}) {
        const sprite = atlas.createSprite(regionName, x, y, options);
        if (sprite) {
            this.sprites[id] = sprite;
        }
        return sprite;
    }
    
    /**
     * Get a sprite by ID
     * @param {string} id - The ID of the sprite
     * @returns {Object} - The sprite object
     */
    getSprite(id) {
        return this.sprites[id];
    }
    
    /**
     * Update a sprite's properties
     * @param {string} id - The ID of the sprite
     * @param {Object} properties - The properties to update
     */
    updateSprite(id, properties) {
        const sprite = this.sprites[id];
        if (sprite) {
            this.renderer.updateSprite(sprite.id, properties);
            Object.assign(sprite, properties);
        }
    }
    
    /**
     * Remove a sprite
     * @param {string} id - The ID of the sprite
     */
    removeSprite(id) {
        const sprite = this.sprites[id];
        if (sprite) {
            this.renderer.removeSprite(sprite.id);
            delete this.sprites[id];
        }
    }
    
    /**
     * Create an animation
     * @param {string} id - The ID of the animation
     * @param {Array} frames - The animation frames
     * @param {number} frameRate - The frame rate in frames per second
     * @returns {Object} - The animation object
     */
    createAnimation(id, frames, frameRate = 10) {
        this.animations[id] = {
            frames: frames,
            frameRate: frameRate,
            frameDuration: 1000 / frameRate,
            loop: true
        };
        
        // Store the animation frames
        frames.forEach(frame => {
            if (!this.animationFrames[frame]) {
                console.error(`Animation frame '${frame}' not found`);
            }
        });
        
        return this.animations[id];
    }
    
    /**
     * Add a frame to the animation frames
     * @param {string} id - The ID of the frame
     * @param {string} textureName - The name of the texture
     * @param {Array} texCoords - The texture coordinates [x1, y1, x2, y2]
     */
    addAnimationFrame(id, textureName, texCoords) {
        this.animationFrames[id] = {
            texture: textureName,
            texCoords: texCoords
        };
    }
    
    /**
     * Add a frame from an atlas to the animation frames
     * @param {string} id - The ID of the frame
     * @param {TextureAtlas} atlas - The texture atlas
     * @param {string} regionName - The name of the region
     */
    addAnimationFrameFromAtlas(id, atlas, regionName) {
        const region = atlas.regions[regionName];
        if (!region) {
            console.error(`Region '${regionName}' not found in atlas`);
            return;
        }
        
        this.animationFrames[id] = {
            texture: atlas.name,
            texCoords: region.texCoords
        };
    }
    
    /**
     * Play an animation on a sprite
     * @param {string} spriteId - The ID of the sprite
     * @param {string} animationId - The ID of the animation
     * @param {boolean} loop - Whether to loop the animation
     * @param {Function} onComplete - Callback when the animation completes
     */
    playAnimation(spriteId, animationId, loop = true, onComplete = null) {
        const sprite = this.sprites[spriteId];
        const animation = this.animations[animationId];
        
        if (!sprite || !animation) {
            console.error(`Sprite '${spriteId}' or animation '${animationId}' not found`);
            return;
        }
        
        // Set up the animation state
        sprite.animation = {
            id: animationId,
            frames: animation.frames,
            frameRate: animation.frameRate,
            frameDuration: animation.frameDuration,
            currentFrame: 0,
            lastFrameTime: performance.now(),
            loop: loop,
            onComplete: onComplete
        };
        
        // Set the initial frame
        this.updateAnimationFrame(sprite);
    }
    
    /**
     * Stop an animation on a sprite
     * @param {string} spriteId - The ID of the sprite
     */
    stopAnimation(spriteId) {
        const sprite = this.sprites[spriteId];
        if (sprite) {
            sprite.animation = null;
        }
    }
    
    /**
     * Update all animations
     */
    updateAnimations() {
        const now = performance.now();
        
        Object.values(this.sprites).forEach(sprite => {
            if (sprite.animation) {
                const animation = sprite.animation;
                
                // Check if it's time for the next frame
                if (now - animation.lastFrameTime >= animation.frameDuration) {
                    // Update the frame
                    animation.currentFrame++;
                    animation.lastFrameTime = now;
                    
                    // Check if the animation is complete
                    if (animation.currentFrame >= animation.frames.length) {
                        if (animation.loop) {
                            // Loop back to the beginning
                            animation.currentFrame = 0;
                        } else {
                            // Stop the animation
                            animation.currentFrame = animation.frames.length - 1;
                            
                            // Call the onComplete callback
                            if (animation.onComplete) {
                                animation.onComplete();
                            }
                            
                            // Remove the animation
                            sprite.animation = null;
                        }
                    }
                    
                    // Update the sprite's texture coordinates
                    this.updateAnimationFrame(sprite);
                }
            }
        });
    }
    
    /**
     * Update a sprite's texture coordinates based on its current animation frame
     * @param {Object} sprite - The sprite object
     */
    updateAnimationFrame(sprite) {
        if (!sprite.animation) return;
        
        const frameId = sprite.animation.frames[sprite.animation.currentFrame];
        const frame = this.animationFrames[frameId];
        
        if (frame) {
            // Update the sprite's texture and texture coordinates
            this.renderer.updateSprite(sprite.id, {
                texture: frame.texture,
                texCoords: frame.texCoords
            });
        }
    }
}
