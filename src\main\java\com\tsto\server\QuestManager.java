package com.tsto.server;

import java.util.Map;
import java.util.HashMap;
import java.util.logging.Logger;

public class QuestManager {
    private static final Logger LOGGER = Logger.getLogger(QuestManager.class.getName());
    private static QuestManager instance;
    private final GameState gameState;
    private final Map<String, String> activeQuests; // characterId -> questId

    private QuestManager() {
        this.gameState = GameState.getInstance();
        this.activeQuests = new HashMap<>();
    }

    public static synchronized QuestManager getInstance() {
        if (instance == null) {
            instance = new QuestManager();
        }
        return instance;
    }

    public boolean startQuest(String characterId, String questId) {
        Character character = gameState.getCharacter(characterId);
        Quest quest = gameState.getQuest(questId);

        if (character == null || quest == null || quest.isCompleted() || activeQuests.containsKey(characterId)) {
            return false;
        }

        quest.start();
        activeQuests.put(characterId, questId);
        LOGGER.info("Character " + characterId + " started quest " + questId);
        return true;
    }

    public boolean completeQuest(String characterId) {
        String questId = activeQuests.get(characterId);
        if (questId == null) {
            return false;
        }

        Quest quest = gameState.getQuest(questId);
        if (quest == null || !quest.checkCompletion()) {
            return false;
        }

        // Apply rewards
        Map<String, Integer> rewards = quest.getRewards();
        for (Map.Entry<String, Integer> reward : rewards.entrySet()) {
            switch (reward.getKey()) {
                case "money":
                    gameState.addMoney(reward.getValue());
                    break;
                case "donuts":
                    gameState.addDonuts(reward.getValue());
                    break;
                case "experience":
                    gameState.addExperience(reward.getValue());
                    break;
            }
        }

        quest.complete();
        activeQuests.remove(characterId);
        LOGGER.info("Character " + characterId + " completed quest " + questId);
        return true;
    }

    public boolean updateQuestProgress(String characterId, String objectiveDescription) {
        String questId = activeQuests.get(characterId);
        if (questId == null) {
            return false;
        }

        Quest quest = gameState.getQuest(questId);
        if (quest == null) {
            return false;
        }

        for (Quest.QuestObjective objective : quest.getObjectives()) {
            if (objective.getDescription().equals(objectiveDescription)) {
                objective.incrementProgress();
                LOGGER.info("Updated progress for quest " + questId + " objective: " + objectiveDescription);
                return true;
            }
        }

        return false;
    }

    public String getActiveQuest(String characterId) {
        return activeQuests.get(characterId);
    }

    public Map<String, Integer> getQuestProgress(String characterId) {
        String questId = activeQuests.get(characterId);
        if (questId == null) {
            return null;
        }

        Quest quest = gameState.getQuest(questId);
        if (quest == null) {
            return null;
        }

        Map<String, Integer> progress = new HashMap<>();
        for (Quest.QuestObjective objective : quest.getObjectives()) {
            progress.put(objective.getDescription(), objective.getCurrentCount());
        }

        return progress;
    }

    public void abandonQuest(String characterId) {
        String questId = activeQuests.remove(characterId);
        if (questId != null) {
            Quest quest = gameState.getQuest(questId);
            if (quest != null) {
                quest.setActive(false);
                LOGGER.info("Character " + characterId + " abandoned quest " + questId);
            }
        }
    }
} 