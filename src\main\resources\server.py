from http.server import HTTPServer, BaseHTTPRequestHandler
import json
import os
import hashlib
from database import Database
from datetime import datetime

# Create database instance
db = Database()

class TSTORequestHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/':
            self.path = '/index.html'
        
        try:
            # Serve static files
            if self.path.endswith('.html') or self.path.endswith('.css') or self.path.endswith('.js'):
                self.send_response(200)
                self.send_header('Content-type', self.get_content_type())
                self.end_headers()
                
                with open('src/main/resources/static' + self.path, 'rb') as file:
                    self.wfile.write(file.read())
            else:
                self.send_response(404)
                self.end_headers()
                self.wfile.write(b'File not found')
                
        except Exception as e:
            self.send_response(500)
            self.end_headers()
            self.wfile.write(str(e).encode())

    def do_POST(self):
        try:
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            data = json.loads(post_data.decode('utf-8'))
            
            if self.path == '/api/register':
                response = self.handle_register(data)
            elif self.path == '/api/login':
                response = self.handle_login(data)
            elif self.path == '/api/save':
                response = self.handle_save(data)
            elif self.path == '/api/load':
                response = self.handle_load(data)
            else:
                response = {'success': False, 'message': 'Invalid endpoint'}
                
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(response).encode())
            
        except Exception as e:
            self.send_response(500)
            self.end_headers()
            self.wfile.write(str(e).encode())

    def handle_register(self, data):
        username = data.get('username')
        password = data.get('password')
        email = data.get('email')
        
        if not username or not password:
            return {'success': False, 'message': 'Username and password are required'}
            
        # Hash password
        password_hash = hashlib.sha256(password.encode()).hexdigest()
        
        # Create user
        user_id = db.create_user(username, password_hash, email)
        if not user_id:
            return {'success': False, 'message': 'Username already exists'}
            
        return {'success': True, 'message': 'Registration successful', 'user_id': user_id}

    def handle_login(self, data):
        username = data.get('username')
        password = data.get('password')
        
        if not username or not password:
            return {'success': False, 'message': 'Username and password are required'}
            
        # Get user
        user = db.get_user(username)
        if not user:
            return {'success': False, 'message': 'Invalid username or password'}
            
        # Verify password
        password_hash = hashlib.sha256(password.encode()).hexdigest()
        if password_hash != user[2]:  # password_hash is at index 2
            return {'success': False, 'message': 'Invalid username or password'}
            
        # Get game state
        game_state = db.get_game_state(user[0])  # user_id is at index 0
        
        return {
            'success': True,
            'message': 'Login successful',
            'user_id': user[0],
            'game_state': {
                'money': game_state[2],
                'donuts': game_state[3],
                'buildings': json.loads(game_state[4]),
                'characters': json.loads(game_state[5]),
                'inventory': json.loads(game_state[6])
            }
        }

    def handle_save(self, data):
        user_id = data.get('user_id')
        game_state = data.get('game_state')
        
        if not user_id or not game_state:
            return {'success': False, 'message': 'Invalid save data'}
            
        # Update game state
        db.update_game_state(
            user_id,
            money=game_state.get('money'),
            donuts=game_state.get('donuts'),
            buildings=game_state.get('buildings'),
            characters=game_state.get('characters'),
            inventory=game_state.get('inventory')
        )
        
        return {'success': True, 'message': 'Game saved successfully'}

    def handle_load(self, data):
        user_id = data.get('user_id')
        
        if not user_id:
            return {'success': False, 'message': 'User ID is required'}
            
        # Get game state
        game_state = db.get_game_state(user_id)
        if not game_state:
            return {'success': False, 'message': 'No save game found'}
            
        return {
            'success': True,
            'game_state': {
                'money': game_state[2],
                'donuts': game_state[3],
                'buildings': json.loads(game_state[4]),
                'characters': json.loads(game_state[5]),
                'inventory': json.loads(game_state[6])
            }
        }

    def get_content_type(self):
        if self.path.endswith('.html'):
            return 'text/html'
        elif self.path.endswith('.css'):
            return 'text/css'
        elif self.path.endswith('.js'):
            return 'application/javascript'
        return 'text/plain'

def run(server_class=HTTPServer, handler_class=TSTORequestHandler, port=8081):
    server_address = ('', port)
    httpd = server_class(server_address, handler_class)
    print(f'Starting TSTO Game Server...')
    print(f'Serving HTTP on port {port} (http://localhost:{port}/) ...')
    httpd.serve_forever()

if __name__ == '__main__':
    run() 