package com.tsto.server;

import java.util.HashMap;
import java.util.Map;

public class ShopItem {
    private final String id;
    private final String name;
    private final String type;
    private final int price;
    private final String description;
    private final String imageUrl;
    private final boolean isPremium;
    private final int unlockLevel;
    private final Map<String, Object> properties;

    public ShopItem(String id, String name, String type, int price, String description, String imageUrl, boolean isPremium, int unlockLevel) {
        this.id = id;
        this.name = name;
        this.type = type;
        this.price = price;
        this.description = description;
        this.imageUrl = imageUrl;
        this.isPremium = isPremium;
        this.unlockLevel = unlockLevel;
        this.properties = new HashMap<>();
    }

    public boolean canPurchase(int userLevel, int userMoney, int userDonuts) {
        System.out.println("Debug: Checking purchase for item " + name + " (ID: " + id + ")");
        System.out.println("Debug: User Level: " + userLevel + ", Unlock Level: " + unlockLevel);
        System.out.println("Debug: User Money: " + userMoney + ", User Donuts: " + userDonuts + ", Item Price: " + price);
        if (userLevel < unlockLevel) {
            System.out.println("Debug: Purchase denied - User level too low");
            return false;
        }
        if (isPremium) {
            boolean canBuy = userDonuts >= price;
            System.out.println("Debug: Premium item - Can buy: " + canBuy);
            return canBuy;
        } else {
            boolean canBuy = userMoney >= price;
            System.out.println("Debug: Non-premium item - Can buy: " + canBuy);
            return canBuy;
        }
    }

    public String getCurrencyType() {
        return isPremium ? "donuts" : "money";
    }

    public String getCategory() {
        return type;
    }

    public String getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public String getType() {
        return type;
    }

    public int getPrice() {
        return price;
    }

    public String getDescription() {
        return description;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public boolean isPremium() {
        return isPremium;
    }

    public int getUnlockLevel() {
        return unlockLevel;
    }

    public Map<String, Object> getProperties() {
        return properties;
    }

    public void setProperty(String key, Object value) {
        System.out.println("Debug: Setting property " + key + " to " + value);
        properties.put(key, value);
    }

    public Object getProperty(String key) {
        Object value = properties.get(key);
        System.out.println("Debug: Getting property " + key + " - Value: " + value);
        return value;
    }
} 