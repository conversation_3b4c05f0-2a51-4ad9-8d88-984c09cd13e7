// Global variables
let currentUser = null;
let gameState = null;

// DOM Elements
const loginForm = document.getElementById('login-form');
const registerForm = document.getElementById('register-form');
const gameContent = document.getElementById('game-content');
const townGrid = document.getElementById('town-grid');
const playerInfo = document.getElementById('player-info');
const logoutBtn = document.getElementById('logout-btn');

// Modal Elements
const shopModal = document.getElementById('shop-modal');
const questsModal = document.getElementById('quests-modal');
const charactersModal = document.getElementById('characters-modal');
const friendsModal = document.getElementById('friends-modal');

// Game Feature Buttons
const shopBtn = document.getElementById('shop-btn');
const questsBtn = document.getElementById('quests-btn');
const charactersBtn = document.getElementById('characters-btn');
const friendsBtn = document.getElementById('friends-btn');

// Event Listeners
document.addEventListener('DOMContentLoaded', () => {
    // Check if user is already logged in
    const token = localStorage.getItem('authToken');
    if (token) {
        validateSession(token);
    }

    // Add modal event listeners
    shopBtn?.addEventListener('click', () => showModal(shopModal));
    questsBtn?.addEventListener('click', () => showModal(questsModal));
    charactersBtn?.addEventListener('click', () => showModal(charactersModal));
    friendsBtn?.addEventListener('click', () => showModal(friendsModal));

    // Close buttons for all modals
    document.querySelectorAll('.close').forEach(closeBtn => {
        closeBtn.addEventListener('click', () => {
            const modal = closeBtn.closest('.modal');
            hideModal(modal);
        });
    });

    // Close modals when clicking outside
    window.addEventListener('click', (event) => {
        if (event.target.classList.contains('modal')) {
            hideModal(event.target);
        }
    });
});

loginForm?.addEventListener('submit', handleLogin);
registerForm?.addEventListener('submit', handleRegister);
logoutBtn?.addEventListener('click', handleLogout);

// API Functions
async function validateSession(token) {
    try {
        const response = await fetch('/api/auth/validate', {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        
        if (response.ok) {
            const user = await response.json();
            currentUser = user;
            showGameContent();
            loadGameState();
        } else {
            localStorage.removeItem('authToken');
            showLoginForm();
        }
    } catch (error) {
        console.error('Session validation failed:', error);
        showLoginForm();
    }
}

async function handleLogin(event) {
    event.preventDefault();
    const username = document.getElementById('login-username').value;
    const password = document.getElementById('login-password').value;

    try {
        const response = await fetch('/api/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ username, password })
        });

        const data = await response.json();
        if (data.success) {
            currentUser = {
                id: data.userId,
                username: data.username,
                level: data.level,
                experience: data.experience,
                money: data.money,
                donuts: data.donuts
            };
            localStorage.setItem('authToken', data.token);
            showGameContent();
            loadGameState();
        } else {
            alert(data.message || 'Login failed');
        }
    } catch (error) {
        console.error('Login failed:', error);
        alert('Login failed. Please try again.');
    }
}

async function handleRegister(event) {
    event.preventDefault();
    const username = document.getElementById('register-username').value;
    const password = document.getElementById('register-password').value;

    try {
        const response = await fetch('/api/auth/register', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ username, password })
        });

        if (response.ok) {
            alert('Registration successful! Please login.');
            showLoginForm();
        } else {
            alert('Registration failed. Username might be taken.');
        }
    } catch (error) {
        console.error('Registration failed:', error);
        alert('An error occurred during registration.');
    }
}

async function handleLogout() {
    try {
        await fetch('/api/auth/logout', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('authToken')}`
            }
        });
    } catch (error) {
        console.error('Logout failed:', error);
    } finally {
        localStorage.removeItem('authToken');
        currentUser = null;
        showLoginForm();
    }
}

async function loadGameState() {
    try {
        const response = await fetch('/api/game/state', {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('authToken')}`
            }
        });

        if (response.ok) {
            gameState = await response.json();
            updateGameUI();
        }
    } catch (error) {
        console.error('Failed to load game state:', error);
    }
}

// UI Functions
function showLoginForm() {
    document.getElementById('login-section').style.display = 'block';
    document.getElementById('register-section').style.display = 'none';
    gameContent.style.display = 'none';
}

function showRegisterForm() {
    document.getElementById('login-section').style.display = 'none';
    document.getElementById('register-section').style.display = 'block';
    gameContent.style.display = 'none';
}

function showGameContent() {
    document.getElementById('login-section').style.display = 'none';
    document.getElementById('register-section').style.display = 'none';
    gameContent.style.display = 'block';
}

function updateGameUI() {
    if (!currentUser || !gameState) return;

    // Update player info
    playerInfo.innerHTML = `
        <div>Level: ${currentUser.level}</div>
        <div>Money: ${currentUser.money}</div>
        <div>Donuts: ${currentUser.donuts}</div>
    `;

    // Update town grid
    townGrid.innerHTML = '';
    gameState.buildings.forEach(building => {
        const buildingElement = document.createElement('div');
        buildingElement.className = 'building';
        buildingElement.style.gridColumn = building.x;
        buildingElement.style.gridRow = building.y;
        buildingElement.textContent = building.name;
        townGrid.appendChild(buildingElement);
    });
}

// Helper Functions
function getAuthHeader() {
    return {
        'Authorization': `Bearer ${localStorage.getItem('authToken')}`
    };
}

// Modal Functions
function showModal(modal) {
    modal.style.display = 'block';
    loadModalContent(modal);
}

function hideModal(modal) {
    modal.style.display = 'none';
}

function loadModalContent(modal) {
    const token = localStorage.getItem('authToken');
    if (!token) return;

    if (modal === shopModal) {
        loadShopItems();
    } else if (modal === questsModal) {
        loadQuests();
    } else if (modal === charactersModal) {
        loadCharacters();
    } else if (modal === friendsModal) {
        loadFriends();
    }
}

// Shop Functions
async function loadShopItems() {
    try {
        const response = await fetch('/api/shop/items', {
            headers: getAuthHeader()
        });

        if (response.ok) {
            const items = await response.json();
            const shopItemsContainer = document.getElementById('shop-items');
            shopItemsContainer.innerHTML = '';

            items.forEach(item => {
                const itemElement = document.createElement('div');
                itemElement.className = 'shop-item';
                itemElement.innerHTML = `
                    <h4>${item.name}</h4>
                    <p>${item.description}</p>
                    <p>Price: ${item.price} ${item.currency}</p>
                    <button onclick="purchaseItem('${item.id}')">Buy</button>
                `;
                shopItemsContainer.appendChild(itemElement);
            });
        }
    } catch (error) {
        console.error('Failed to load shop items:', error);
    }
}

async function purchaseItem(itemId) {
    try {
        const response = await fetch(`/api/shop/purchase/${itemId}`, {
            method: 'POST',
            headers: getAuthHeader()
        });

        if (response.ok) {
            const result = await response.json();
            alert(result.message);
            loadGameState(); // Refresh game state
        } else {
            alert('Failed to purchase item');
        }
    } catch (error) {
        console.error('Purchase failed:', error);
    }
}

// Quest Functions
async function loadQuests() {
    try {
        const response = await fetch('/api/quests', {
            headers: getAuthHeader()
        });

        if (response.ok) {
            const quests = await response.json();
            const questsListContainer = document.getElementById('quests-list');
            questsListContainer.innerHTML = '';

            quests.forEach(quest => {
                const questElement = document.createElement('div');
                questElement.className = 'quest-item';
                questElement.innerHTML = `
                    <h4>${quest.name}</h4>
                    <p>${quest.description}</p>
                    <p>Reward: ${quest.reward}</p>
                    <button onclick="startQuest('${quest.id}')" ${quest.isAvailable ? '' : 'disabled'}>
                        ${quest.isAvailable ? 'Start Quest' : 'Unavailable'}
                    </button>
                `;
                questsListContainer.appendChild(questElement);
            });
        }
    } catch (error) {
        console.error('Failed to load quests:', error);
    }
}

async function startQuest(questId) {
    try {
        const response = await fetch(`/api/quests/start/${questId}`, {
            method: 'POST',
            headers: getAuthHeader()
        });

        if (response.ok) {
            alert('Quest started successfully!');
            loadQuests(); // Refresh quests list
        } else {
            alert('Failed to start quest');
        }
    } catch (error) {
        console.error('Failed to start quest:', error);
    }
}

// Character Functions
async function loadCharacters() {
    try {
        const response = await fetch('/api/characters', {
            headers: getAuthHeader()
        });

        if (response.ok) {
            const characters = await response.json();
            const charactersListContainer = document.getElementById('characters-list');
            charactersListContainer.innerHTML = '';

            characters.forEach(character => {
                const characterElement = document.createElement('div');
                characterElement.className = 'character-item';
                characterElement.innerHTML = `
                    <h4>${character.name}</h4>
                    <p>Level: ${character.level}</p>
                    <p>Status: ${character.isAvailable ? 'Available' : 'Busy'}</p>
                    <button onclick="assignTask('${character.id}')" ${character.isAvailable ? '' : 'disabled'}>
                        Assign Task
                    </button>
                `;
                charactersListContainer.appendChild(characterElement);
            });
        }
    } catch (error) {
        console.error('Failed to load characters:', error);
    }
}

async function assignTask(characterId) {
    try {
        const response = await fetch(`/api/characters/assign/${characterId}`, {
            method: 'POST',
            headers: getAuthHeader()
        });

        if (response.ok) {
            alert('Task assigned successfully!');
            loadCharacters(); // Refresh characters list
        } else {
            alert('Failed to assign task');
        }
    } catch (error) {
        console.error('Failed to assign task:', error);
    }
}

// Friend System Functions
async function loadFriends() {
    try {
        const response = await fetch('/api/friends', {
            headers: getAuthHeader()
        });

        if (response.ok) {
            const data = await response.json();
            const friendsListContainer = document.getElementById('friends-list');
            const requestsContainer = document.getElementById('friend-requests');

            // Display friends list
            friendsListContainer.innerHTML = '<h4>Friends</h4>';
            data.friends.forEach(friend => {
                const friendElement = document.createElement('div');
                friendElement.className = 'friend-item';
                friendElement.innerHTML = `
                    <span>${friend.username}</span>
                    <div class="friend-actions">
                        <button onclick="visitTown('${friend.userId}')">Visit Town</button>
                        <button onclick="removeFriend('${friend.userId}')">Remove</button>
                    </div>
                `;
                friendsListContainer.appendChild(friendElement);
            });

            // Display friend requests
            requestsContainer.innerHTML = '<h4>Friend Requests</h4>';
            data.requests.forEach(request => {
                const requestElement = document.createElement('div');
                requestElement.className = 'request-item';
                requestElement.innerHTML = `
                    <span>${request.username}</span>
                    <div class="friend-actions">
                        <button onclick="acceptFriendRequest('${request.userId}')">Accept</button>
                        <button onclick="rejectFriendRequest('${request.userId}')">Reject</button>
                    </div>
                `;
                requestsContainer.appendChild(requestElement);
            });
        }
    } catch (error) {
        console.error('Failed to load friends:', error);
    }
}

async function visitTown(friendId) {
    try {
        const response = await fetch(`/api/friends/visit/${friendId}`, {
            headers: getAuthHeader()
        });

        if (response.ok) {
            const townData = await response.json();
            // TODO: Implement town visiting view
            alert('Visiting friend\'s town...');
        } else {
            alert('Failed to visit friend\'s town');
        }
    } catch (error) {
        console.error('Failed to visit town:', error);
    }
}

async function removeFriend(friendId) {
    if (!confirm('Are you sure you want to remove this friend?')) return;

    try {
        const response = await fetch(`/api/friends/remove/${friendId}`, {
            method: 'POST',
            headers: getAuthHeader()
        });

        if (response.ok) {
            alert('Friend removed successfully');
            loadFriends(); // Refresh friends list
        } else {
            alert('Failed to remove friend');
        }
    } catch (error) {
        console.error('Failed to remove friend:', error);
    }
}

async function acceptFriendRequest(userId) {
    try {
        const response = await fetch(`/api/friends/accept/${userId}`, {
            method: 'POST',
            headers: getAuthHeader()
        });

        if (response.ok) {
            alert('Friend request accepted');
            loadFriends(); // Refresh friends list
        } else {
            alert('Failed to accept friend request');
        }
    } catch (error) {
        console.error('Failed to accept friend request:', error);
    }
}

async function rejectFriendRequest(userId) {
    try {
        const response = await fetch(`/api/friends/reject/${userId}`, {
            method: 'POST',
            headers: getAuthHeader()
        });

        if (response.ok) {
            alert('Friend request rejected');
            loadFriends(); // Refresh friends list
        } else {
            alert('Failed to reject friend request');
        }
    } catch (error) {
        console.error('Failed to reject friend request:', error);
    }
}

function showDailyReward() {
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.innerHTML = `
        <div class="modal-content">
            <h2>Daily Reward</h2>
            <p>Claim your daily reward to get extra money and donuts!</p>
            <div class="reward-info">
                <p>Current Streak: <span id="rewardStreak">0</span> days</p>
                <p>Next Reward: <span id="nextReward">$100</span></p>
            </div>
            <div class="modal-buttons">
                <button onclick="claimDailyReward()">Claim Reward</button>
                <button onclick="closeModal()">Close</button>
            </div>
        </div>
    `;
    document.body.appendChild(modal);
    updateRewardInfo();
}

function closeModal() {
    const modal = document.querySelector('.modal');
    if (modal) {
        modal.remove();
    }
}

function updateRewardInfo() {
    const streak = gameState.dailyRewardStreak || 0;
    const nextReward = 100 * (streak + 1);
    document.getElementById('rewardStreak').textContent = streak;
    document.getElementById('nextReward').textContent = `$${nextReward}`;
}

function claimDailyReward() {
    fetch('/api/claimDailyReward', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            username: gameState.username
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            gameState.money += data.reward.money;
            gameState.donuts += data.reward.donuts;
            gameState.dailyRewardStreak = data.streak;
            updatePlayerStats();
            showRewardPopup(data.reward);
            closeModal();
        } else {
            alert(data.error || 'Failed to claim reward');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Failed to claim reward');
    });
}

function showRewardPopup(reward) {
    const popup = document.createElement('div');
    popup.className = 'reward-popup';
    popup.innerHTML = `
        <div class="reward-content">
            <h3>Reward Claimed!</h3>
            <p>Money: +$${reward.money}</p>
            ${reward.donuts > 0 ? `<p>Donuts: +${reward.donuts}</p>` : ''}
            <button onclick="this.parentElement.parentElement.remove()">OK</button>
        </div>
    `;
    document.body.appendChild(popup);
}

// API endpoints
const API = {
    register: '/api/register',
    login: '/api/login',
    save: '/api/save',
    load: '/api/load'
}; 