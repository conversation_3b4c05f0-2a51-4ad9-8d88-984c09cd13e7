package com.tsto.server;

public class Building {
    private String id;
    private String name;
    private String ownerId;
    private int x;
    private int y;
    private int level;
    private boolean isPlaced;
    private boolean isUnderConstruction;

    public Building(String id, String name) {
        this.id = id;
        this.name = name;
        this.level = 1;
        this.isPlaced = false;
        this.isUnderConstruction = true;
    }

    public String getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public String getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(String ownerId) {
        this.ownerId = ownerId;
    }

    public int getX() {
        return x;
    }

    public void setX(int x) {
        this.x = x;
    }

    public int getY() {
        return y;
    }

    public void setY(int y) {
        this.y = y;
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public boolean isPlaced() {
        return isPlaced;
    }

    public void setPlaced(boolean placed) {
        isPlaced = placed;
    }

    public boolean isUnderConstruction() {
        return isUnderConstruction;
    }

    public void setUnderConstruction(boolean underConstruction) {
        isUnderConstruction = underConstruction;
    }
} 