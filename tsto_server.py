#!/usr/bin/env python3
"""
TSTO Web Game Server
A simple HTTP server for The Simpsons: Tapped Out web game
with debugging capabilities and API endpoints.
"""

import os
import sys
import json
import logging
import argparse
from http.server import HTTPServer, SimpleHTTPRequestHandler
from urllib.parse import parse_qs, urlparse

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger('TSTOServer')

# Also print directly to stdout for debugging
def debug_print(message):
    print(f"DEBUG: {message}", flush=True)

# Default user data for testing
DEFAULT_USER = {
    'userId': 'user123',
    'username': 'testuser',
    'level': 1,
    'experience': 0,
    'money': 1000,
    'donuts': 10
}

class TSTORequestHandler(SimpleHTTPRequestHandler):
    """Custom request handler for TSTO web game"""

    def __init__(self, *args, **kwargs):
        # Store the static directory path
        self.static_dir = os.path.join(os.getcwd(), "src", "main", "resources", "static")

        debug_print(f"Initializing handler with static directory: {self.static_dir}")

        # Check if the directory exists
        if not os.path.exists(self.static_dir):
            debug_print(f"Static directory not found at: {self.static_dir}")
            # Try to find the static directory
            for root, dirs, _ in os.walk(os.getcwd()):
                if "static" in dirs:
                    self.static_dir = os.path.join(root, "static")
                    debug_print(f"Found static directory at: {self.static_dir}")
                    break

        # Set the directory to serve files from
        debug_print(f"Using directory: {self.static_dir}")
        super().__init__(*args, directory=self.static_dir, **kwargs)

    def log_message(self, format, *args):
        """Override log_message to use our logger"""
        logger.info(f"{self.address_string()} - {format % args}")

    def do_GET(self):
        """Handle GET requests"""
        debug_print(f"GET request: {self.path}")

        # Special handling for API endpoints
        if self.path.startswith('/api/'):
            debug_print(f"Handling API GET request: {self.path}")
            self.handle_api_get()
            return

        # Default to serving static files
        debug_print(f"Serving static file: {self.path}")
        return super().do_GET()

    def do_POST(self):
        """Handle POST requests"""
        debug_print(f"POST request: {self.path}")

        # Handle API endpoints
        if self.path.startswith('/api/'):
            debug_print(f"Handling API POST request: {self.path}")
            self.handle_api_post()
            return

        # Default response for non-API POST requests
        debug_print(f"Unknown POST request: {self.path}")
        self.send_error(404, "Not Found")

    def do_OPTIONS(self):
        """Handle OPTIONS requests (for CORS)"""
        logger.info(f"OPTIONS request: {self.path}")
        self.send_response(200)
        self.send_cors_headers()
        self.end_headers()

    def handle_api_get(self):
        """Handle GET requests to API endpoints"""
        parts = self.path.split('/')
        endpoint = parts[2] if len(parts) > 2 else None

        if endpoint == 'gameState':
            # Return game state for the user
            user_id = self.get_query_param('userId')
            logger.info(f"Getting game state for user: {user_id}")
            self.send_json_response({
                'success': True,
                'gameState': {
                    'money': 1000,
                    'donuts': 10,
                    'level': 1,
                    'experience': 0,
                    'buildings': [],
                    'characters': [
                        {'name': 'Homer', 'status': 'idle'},
                        {'name': 'Marge', 'status': 'idle'}
                    ]
                }
            })
        else:
            self.send_error(404, f"API endpoint not found: {endpoint}")

    def handle_api_post(self):
        """Handle POST requests to API endpoints"""
        content_length = int(self.headers.get('Content-Length', 0))
        if content_length > 0:
            post_data = self.rfile.read(content_length)
            try:
                data = json.loads(post_data.decode('utf-8'))
                logger.info(f"POST data: {data}")
            except json.JSONDecodeError:
                logger.error("Failed to parse JSON data")
                self.send_error(400, "Invalid JSON")
                return
        else:
            data = {}

        parts = self.path.split('/')
        endpoint = parts[2] if len(parts) > 2 else None

        if endpoint == 'login':
            self.handle_login(data)
        elif endpoint == 'register':
            self.handle_register(data)
        elif endpoint == 'saveGame':
            self.handle_save_game(data)
        elif endpoint == 'purchase':
            self.handle_purchase(data)
        else:
            self.send_error(404, f"API endpoint not found: {endpoint}")

    def handle_login(self, data):
        """Handle login requests"""
        username = data.get('username')
        password = data.get('password')  # Not used but kept for clarity

        debug_print(f"Login attempt: {username}")

        # For testing, accept any login
        response = {
            'success': True,
            'userId': DEFAULT_USER['userId'],
            'username': username,
            'level': DEFAULT_USER['level'],
            'experience': DEFAULT_USER['experience'],
            'money': DEFAULT_USER['money'],
            'donuts': DEFAULT_USER['donuts'],
            'token': 'dummy_token',
            'message': 'Login successful'
        }

        debug_print(f"Login response: {response}")
        self.send_json_response(response)

    def handle_register(self, data):
        """Handle registration requests"""
        username = data.get('username')
        password = data.get('password')  # Not used but kept for clarity

        debug_print(f"Registration attempt: {username}")

        # For testing, accept any registration
        response = {
            'success': True,
            'userId': DEFAULT_USER['userId'],
            'username': username,
            'level': DEFAULT_USER['level'],
            'experience': DEFAULT_USER['experience'],
            'money': DEFAULT_USER['money'],
            'donuts': DEFAULT_USER['donuts'],
            'message': 'Registration successful'
        }

        debug_print(f"Registration response: {response}")
        self.send_json_response(response)

    def handle_save_game(self, data):
        """Handle save game requests"""
        user_id = data.get('userId')
        game_state = data.get('gameState', {})

        logger.info(f"Saving game for user: {user_id}")
        logger.debug(f"Game state: {game_state}")

        self.send_json_response({
            'success': True,
            'message': 'Game saved successfully'
        })

    def handle_purchase(self, data):
        """Handle purchase requests"""
        user_id = data.get('userId')
        item_id = data.get('itemId')

        logger.info(f"Purchase request: User {user_id}, Item {item_id}")

        self.send_json_response({
            'success': True,
            'message': 'Purchase successful',
            'money': 900,  # Simulated updated money after purchase
            'donuts': 10
        })

    def send_json_response(self, data):
        """Send a JSON response"""
        debug_print(f"Sending JSON response: {data}")
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_cors_headers()
        self.end_headers()
        response_json = json.dumps(data)
        debug_print(f"Response JSON: {response_json}")
        self.wfile.write(response_json.encode())

    def send_cors_headers(self):
        """Send CORS headers"""
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')

    def get_query_param(self, param_name):
        """Get a query parameter from the URL"""
        query = urlparse(self.path).query
        params = parse_qs(query)
        return params.get(param_name, [''])[0]

def run_server(port=8081, debug=False):
    """Run the server"""
    if debug:
        logger.setLevel(logging.DEBUG)

    server_address = ('', port)
    httpd = HTTPServer(server_address, TSTORequestHandler)

    debug_print(f"Starting TSTO server on port {port}...")
    debug_print(f"Current working directory: {os.getcwd()}")

    # List files in the static directory
    static_dir = os.path.join(os.getcwd(), "src", "main", "resources", "static")
    if os.path.exists(static_dir):
        debug_print(f"Files in {static_dir}:")
        for file in os.listdir(static_dir):
            debug_print(f"  - {file}")
    else:
        debug_print(f"Directory does not exist: {static_dir}")
        # Try to find the static directory
        for root, dirs, _ in os.walk(os.getcwd()):
            if "static" in dirs:
                static_dir = os.path.join(root, "static")
                debug_print(f"Found static directory at: {static_dir}")
                if os.path.exists(static_dir):
                    debug_print(f"Files in {static_dir}:")
                    for file in os.listdir(static_dir):
                        debug_print(f"  - {file}")
                break

    debug_print("Server is ready to accept connections")
    print("Server is running at http://localhost:" + str(port))

    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        debug_print("Server stopped by user")
    except Exception as e:
        debug_print(f"Server error: {e}")
        raise

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='TSTO Web Game Server')
    parser.add_argument('-p', '--port', type=int, default=8081, help='Port to run the server on')
    parser.add_argument('-d', '--debug', action='store_true', help='Enable debug mode')
    args = parser.parse_args()

    run_server(port=args.port, debug=args.debug)
